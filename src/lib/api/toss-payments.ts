import getEnv from '@/lib/config/get-env';

export interface TossPaymentConfirmRequest {
  paymentKey: string;
  orderId: string;
  amount: number;
}

export interface TossPaymentConfirmResponse {
  paymentKey: string;
  orderId: string;
  status: string;
  totalAmount: number;
  method: string;
  requestedAt: string;
  approvedAt: string;
  receipt?: {
    url: string;
  };
  checkout?: {
    url: string;
  };
}

export class TossPaymentError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number
  ) {
    super(message);
    this.name = 'TossPaymentError';
  }
}

export async function confirmTossPayment(
  params: TossPaymentConfirmRequest
): Promise<TossPaymentConfirmResponse> {
  try {
    const secretKey = getEnv('TOSS_SECRET_KEY');

    if (!secretKey) {
      throw new TossPaymentError(
        '토스페이먼츠 시크릿 키가 설정되지 않았습니다.'
      );
    }

    // Base64로 인코딩된 시크릿 키 생성
    const encodedSecretKey = Buffer.from(secretKey + ':').toString('base64');

    const response = await fetch(
      'https://api.tosspayments.com/v1/payments/confirm',
      {
        method: 'POST',
        headers: {
          Authorization: `Basic ${encodedSecretKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      throw new TossPaymentError(
        data.message || '결제 승인에 실패했습니다.',
        data.code,
        response.status
      );
    }

    return data;
  } catch (error) {
    if (error instanceof TossPaymentError) {
      throw error;
    }
    throw new TossPaymentError('결제 승인 요청 중 오류가 발생했습니다.');
  }
}


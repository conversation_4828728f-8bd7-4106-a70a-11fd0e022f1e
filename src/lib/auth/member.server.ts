
import { NextRequest } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';

/**
 * 서버용 멤버 인증 유틸리티
 * API 라우트와 서버 컴포넌트에서 사용
 */

export interface AuthenticatedMember {
  id: string;           // members 테이블의 PK (member_id)
  nickname: string;
  email?: string;
  profileImageUrl?: string;
  phone?: string;
  gender?: string;
  birthYear?: number;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface MemberAuthResult {
  success: boolean;
  member?: AuthenticatedMember;
  error?: string;
  statusCode?: number;
}

/**
 * NextRequest에서 Supabase 서버 클라이언트 생성
 */
function createSupabaseFromRequest(request: NextRequest) {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // NextRequest는 읽기 전용이므로 설정 작업은 생략
        },
        remove(name: string, options: CookieOptions) {
          // NextRequest는 읽기 전용이므로 제거 작업은 생략
        },
      },
    }
  );
}

/**
 * cookies()를 사용한 Supabase 서버 클라이언트 생성 (서버 컴포넌트용)
 */
function createSupabaseFromCookies() {
  const cookieStore = cookies();
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        async get(name: string) {
          return (await cookieStore).get(name)?.value;
        },
      },
    }
  );
}

/**
 * NextRequest에서 멤버 정보 추출
 * API 라우트에서 사용
 */
export async function getMemberFromRequest(request: NextRequest): Promise<MemberAuthResult> {
  try {
    const supabase = createSupabaseFromRequest(request);

    // 1. 사용자 인증 확인
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: '로그인이 필요합니다.',
        statusCode: 401,
      };
    }

    // 2. 멤버 정보 조회 (user_id로 members 테이블에서 조회)
    const { data: member, error: memberError } = await supabase
      .from('members')
      .select('*')
      .eq('id', user.id)
      .single();

    if (memberError) {
      console.error('멤버 정보 조회 오류:', memberError);
      
      if (memberError.code === 'PGRST116') {
        return {
          success: false,
          error: '회원 정보를 찾을 수 없습니다. 회원가입을 완료해주세요.',
          statusCode: 403,
        };
      }

      return {
        success: false,
        error: '회원 정보 조회 중 오류가 발생했습니다.',
        statusCode: 500,
      };
    }

    if (!member) {
      return {
        success: false,
        error: '회원 정보를 찾을 수 없습니다.',
        statusCode: 403,
      };
    }

    // 3. 성공 응답
    return {
      success: true,
      member: {
        id: member.id,                    // members 테이블의 PK
        nickname: member.nickname,
        email: member.email,
        profileImageUrl: member.profile_image_url,
        phone: member.phone,
        gender: member.gender,
        birthYear: member.birth_year,
        role: member.role,
        createdAt: member.created_at,
        updatedAt: member.updated_at,
      },
    };

  } catch (error) {
    console.error('멤버 인증 오류:', error);
    return {
      success: false,
      error: '서버 오류가 발생했습니다.',
      statusCode: 500,
    };
  }
}

/**
 * 서버 컴포넌트에서 멤버 정보 추출
 */
export async function getMemberFromCookies(): Promise<MemberAuthResult> {
  try {
    const supabase = createSupabaseFromCookies();

    // 1. 사용자 인증 확인
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return {
        success: false,
        error: '로그인이 필요합니다.',
        statusCode: 401,
      };
    }

    // 2. 멤버 정보 조회
    const { data: member, error: memberError } = await supabase
      .from('members')
      .select('*')
      .eq('id', user.id)
      .single();

    if (memberError) {
      console.error('멤버 정보 조회 오류:', memberError);
      
      if (memberError.code === 'PGRST116') {
        return {
          success: false,
          error: '회원 정보를 찾을 수 없습니다. 회원가입을 완료해주세요.',
          statusCode: 403,
        };
      }

      return {
        success: false,
        error: '회원 정보 조회 중 오류가 발생했습니다.',
        statusCode: 500,
      };
    }

    if (!member) {
      return {
        success: false,
        error: '회원 정보를 찾을 수 없습니다.',
        statusCode: 403,
      };
    }

    // 3. 성공 응답
    return {
      success: true,
      member: {
        id: member.id,
        nickname: member.nickname,
        email: member.email,
        profileImageUrl: member.profile_image_url,
        phone: member.phone,
        gender: member.gender,
        birthYear: member.birth_year,
        role: member.role,
        createdAt: member.created_at,
        updatedAt: member.updated_at,
      },
    };

  } catch (error) {
    console.error('멤버 인증 오류:', error);
    return {
      success: false,
      error: '서버 오류가 발생했습니다.',
      statusCode: 500,
    };
  }
}

/**
 * 멤버 인증 필수 체크
 * API 라우트에서 사용하며, 인증 실패 시 NextResponse 반환
 */
export async function requireMemberAuth(request: NextRequest): Promise<{
  member?: AuthenticatedMember;
  errorResponse?: Response;
}> {
  const authResult = await getMemberFromRequest(request);
  
  if (!authResult.success) {
    const { error, statusCode } = authResult;
    return {
      errorResponse: new Response(
        JSON.stringify({ 
          error: statusCode === 401 ? 'UNAUTHORIZED' : 'FORBIDDEN',
          message: error
        }),
        {
          status: statusCode || 500,
          headers: { 'Content-Type': 'application/json' },
        }
      ),
    };
  }

  return {
    member: authResult.member!,
  };
}

/**
 * 멤버 ID만 빠르게 추출하는 헬퍼 함수
 */
export async function getMemberIdFromRequest(request: NextRequest): Promise<string | null> {
  const authResult = await getMemberFromRequest(request);
  return authResult.success ? authResult.member!.id : null;
}

/**
 * 서버 컴포넌트용 멤버 ID 추출
 */
export async function getMemberIdFromCookies(): Promise<string | null> {
  const authResult = await getMemberFromCookies();
  return authResult.success ? authResult.member!.id : null;
}
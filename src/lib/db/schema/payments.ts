import { pgTable, pgEnum, uuid, text, integer, timestamp, json, index, unique } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { enrollments } from './enrollments';

// Enum 정의
export const paymentTypeEnum = pgEnum('payment_type', [
  'deposit', 'remaining', 'refund'
]);

export const paymentStatusEnum = pgEnum('payment_status', [
  'pending', 'paid', 'failed', 'cancelled', 'refunded'
]);

export const paymentEventTypeEnum = pgEnum('payment_event_type', [
  'payment_prepared',
  'payment_requested', 
  'payment_succeeded',
  'payment_failed',
  'payment_cancelled',
  'refund_requested',
  'refund_completed',
  'webhook_received'
]);

// PG사별 추가 데이터 타입 정의
type PaymentData = {
  method?: string;
  cardType?: string;
  lastFourDigits?: string;
  issuerName?: string;
  acquirerName?: string;
  installmentPlanMonths?: number;
  approvedAt?: string;
  [key: string]: any;
};

// payments 테이블
export const payments = pgTable('payments', {
  id: uuid().primaryKey().defaultRandom(),
  enrollmentId: uuid('enrollment_id').notNull(),
  
  // 결제 정보
  amount: integer('amount').notNull(),
  paymentType: paymentTypeEnum().notNull().default('deposit'),
  
  // PG사 정보
  provider: text('provider').notNull().default('tosspayments'),
  externalPaymentKey: text('external_payment_key'),
  externalOrderId: text('external_order_id'),
  paymentMethod: text('payment_method'),
  
  // PG사별 추가 데이터 (JSON)
  paymentData: json().$type<PaymentData>(),
  
  // 상태
  status: paymentStatusEnum().notNull().default('pending'),
  
  // 타임스탬프
  createdAt: timestamp('created_at', { precision: 6, withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { precision: 6, withTimezone: true }).$onUpdate(() => new Date()),
  paidAt: timestamp('paid_at', { precision: 6, withTimezone: true }),
  failedAt: timestamp('failed_at', { precision: 6, withTimezone: true }),
  refundedAt: timestamp('refunded_at', { precision: 6, withTimezone: true }),
}, (table) => ({
  // 인덱스
  enrollmentIdx: index('idx_payments_enrollment').on(table.enrollmentId),
  providerKeyIdx: index('idx_payments_provider_key').on(table.provider, table.externalPaymentKey),
  statusIdx: index('idx_payments_status').on(table.status),
  createdIdx: index('idx_payments_created').on(table.createdAt),
  paidIdx: index('idx_payments_paid').on(table.paidAt),
  typeIdx: index('idx_payments_type').on(table.paymentType),
  
  // 유니크 제약
  uniqueExternalKey: unique('payments_unique_external_key').on(table.externalPaymentKey),
}));

// 이벤트 데이터 타입 정의
type PaymentEventData = {
  amount?: number;
  paymentKey?: string;
  enrollmentOrder?: number;
  reason?: string;
  webhookType?: string;
  [key: string]: any;
};

// payment_events 테이블
export const paymentEvents = pgTable('payment_events', {
  id: uuid().primaryKey().defaultRandom(),
  paymentId: uuid('payment_id').notNull(),
  enrollmentId: uuid('enrollment_id').notNull(),
  
  // 이벤트 정보
  eventType: paymentEventTypeEnum().notNull(),
  
  // 이벤트 데이터 및 메타정보
  eventData: json().$type<PaymentEventData>(),
  errorMessage: text('error_message'),
  correlationId: uuid('correlation_id'),
  
  // 타임스탬프
  createdAt: timestamp('created_at', { precision: 6, withTimezone: true }).defaultNow(),
}, (table) => ({
  // 인덱스
  paymentIdx: index('idx_payment_events_payment').on(table.paymentId),
  enrollmentIdx: index('idx_payment_events_enrollment').on(table.enrollmentId),
  typeIdx: index('idx_payment_events_type').on(table.eventType),
  createdIdx: index('idx_payment_events_created').on(table.createdAt),
  correlationIdx: index('idx_payment_events_correlation').on(table.correlationId),
}));

// 타입 추출
export type Payment = typeof payments.$inferSelect;
export type NewPayment = typeof payments.$inferInsert;
export type PaymentUpdate = Partial<Pick<Payment, 'status' | 'externalPaymentKey' | 'paymentMethod' | 'paymentData' | 'paidAt' | 'failedAt' | 'refundedAt' | 'updatedAt'>>;

export type PaymentEvent = typeof paymentEvents.$inferSelect;
export type NewPaymentEvent = typeof paymentEvents.$inferInsert;

// Relations 정의
export const enrollmentRelations = relations(enrollments, ({ many }) => ({
  payments: many(payments),
  paymentEvents: many(paymentEvents),
}));

export const paymentRelations = relations(payments, ({ one, many }) => ({
  enrollment: one(enrollments, {
    fields: [payments.enrollmentId],
    references: [enrollments.id],
  }),
  events: many(paymentEvents),
}));

export const paymentEventRelations = relations(paymentEvents, ({ one }) => ({
  payment: one(payments, {
    fields: [paymentEvents.paymentId],
    references: [payments.id],
  }),
  enrollment: one(enrollments, {
    fields: [paymentEvents.enrollmentId],
    references: [enrollments.id],
  }),
}));
import { z } from 'zod';

// 전문 분야 선택 옵션
export const SPECIALTY_OPTIONS = [
  '요가',
  '필라테스',
  '헬스',
  '수영',
  '복싱',
  '러닝',
  '댄스',
  '클라이밍',
  '근력/체중운동',
] as const;

// 성별 옵션
export const GENDER_OPTIONS = {
  MALE: 'male',
  FEMALE: 'female',
} as const;

// 링크 스키마 (studios.links 참고)
export const InstructorLinksSchema = z.object({
  website: z.url({ message: '올바른 웹사이트 URL을 입력해주세요' }).optional(),
  sns: z.url({ message: '올바른 SNS URL을 입력해주세요' }).optional(),
}).optional();

// 이미지 스키마 (스튜디오와 동일한 구조)
export const InstructorImageSchema = z.object({
  path: z.string().min(1, '이미지 경로를 입력해주세요'),
  url: z.url({ message: '올바른 이미지 URL을 입력해주세요' }),
});

// 전문 분야 스키마
export const SpecialtySchema = z.object({
  type: z.enum(SPECIALTY_OPTIONS, { 
    message: '올바른 전문 분야를 선택해주세요' 
  }),
  years: z.number()
    .int('경력은 정수여야 합니다')
    .min(1, '경력은 최소 1년 이상이어야 합니다')
    .max(50, '경력은 최대 50년까지 입력 가능합니다'),
});

// 자격증 스키마
export const CertificateSchema = z.object({
  name: z.string()
    .min(1, '자격증명을 입력해주세요')
    .max(100, '자격증명은 최대 100자까지 입력 가능합니다'),
  issuing_organization: z.string()
    .min(1, '발급 기관을 입력해주세요')
    .max(100, '발급 기관은 최대 100자까지 입력 가능합니다'),
  issue_date: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, '날짜 형식은 YYYY-MM-DD여야 합니다'),
  expiry_date: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, '날짜 형식은 YYYY-MM-DD여야 합니다')
    .optional(),
  certificate_number: z.string()
    .max(50, '자격증 번호는 최대 50자까지 입력 가능합니다')
    .optional(),
});

// 강사 생성 스키마
export const CreateInstructorSchema = z.object({
  // 기본 정보
  name: z.string()
    .min(1, '강사 이름을 입력해주세요')
    .max(30, '강사 이름은 최대 30자까지 입력 가능합니다'),
  gender: z.enum([GENDER_OPTIONS.MALE, GENDER_OPTIONS.FEMALE], {
    message: '성별을 선택해주세요',
  }),
  contact: z.string()
    .max(100, '연락처는 최대 100자까지 입력 가능합니다')
    .optional(),
  description: z.string()
    .min(1, '강사 소개를 입력해주세요')
    .max(300, '강사 소개는 최대 300자까지 입력 가능합니다'),
  
  // 링크 정보
  links: InstructorLinksSchema,
  
  // 이미지 정보
  profileImages: z.array(InstructorImageSchema)
    .max(2, '프로필 이미지는 최대 2장까지 업로드 가능합니다')
    .optional(),
  
  // 경력 정보
  experienceTotalYears: z.number()
    .int('총 경력은 정수여야 합니다')
    .min(1, '총 경력은 최소 1년 이상이어야 합니다')
    .max(20, '총 경력은 최대 20년까지 입력 가능합니다'),
  specialties: z.array(SpecialtySchema)
    .min(1, '최소 1개 이상의 전문 분야를 선택해주세요'),
  
  // 자격증 정보
  certificates: z.array(CertificateSchema).optional(),
});

// 강사 수정 스키마 (모든 필드가 선택사항)
export const UpdateInstructorSchema = CreateInstructorSchema.partial();

// 타입 추론
export type CreateInstructorInput = z.infer<typeof CreateInstructorSchema>;
export type UpdateInstructorInput = z.infer<typeof UpdateInstructorSchema>;
export type InstructorLinks = z.infer<typeof InstructorLinksSchema>;
export type InstructorImage = z.infer<typeof InstructorImageSchema>;
export type Specialty = z.infer<typeof SpecialtySchema>;
export type Certificate = z.infer<typeof CertificateSchema>;
import { z } from 'zod';
import { getByPath, Path } from 'dot-path-value';

const appConfigSchema = z.object({
  app: z.object({
    baseUrl: z.url({ message: 'Base URL must be a valid URL' }),
    // environment: z.enum(['development', 'production', 'test']),
  }),
  kakao: z.object({
    apiKey: z.string().min(1, 'Kakao API key is required'),
  }),
  // supabase: z.object({
  //   url: z.string().url('Supabase URL must be a valid URL'),
  //   // anonKey: z.string().min(1, 'Supabase anon key is required'),
  //   // serviceRoleKey: z.string().min(1, 'Supabase service role key is required'),
  // }),
  // database: z.object({
  //   url: z.string().url('Database URL must be a valid URL'),
  // }),
  // auth: z.object({
  //   secret: z.string().min(32, 'Auth secret must be at least 32 characters'),
  //   url: z.string().url('Auth URL must be a valid URL'),
  //   kakao: z.object({
  //     restApiKey: z.string().min(1, 'Kakao REST API key is required'),
  //     clientSecret: z.string().min(1, 'Kakao client secret is required'),
  //   }),
  // }),
  // payment: z.object({
  //   portone: z.object({
  //     userCode: z.string().min(1, 'PortOne user code is required'),
  //     apiKey: z.string().min(1, 'PortOne API key is required'),
  //     apiSecret: z.string().min(1, 'PortOne API secret is required'),
  //   }),
  // }),
  // maps: z.object({
  //   naver: z.object({
  //     clientId: z.string().min(1, 'Naver Map client ID is required'),
  //   }),
  // }),
});

type AppConfig = z.infer<typeof appConfigSchema>;

class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig;

  private constructor() {
    this.config = this.loadConfiguration();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  public getConfig(): Readonly<AppConfig> {
    return Object.freeze({ ...this.config });
  }

  public getValue<T extends Path<AppConfig>>(path: T) {
    return getByPath(this.config, path);
  }

  private loadConfiguration(): AppConfig {
    const rawConfig = {
      app: {
        baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
        environment: process.env.NODE_ENV || 'development',
      },
      supabase: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
      },
      database: {
        url: process.env.DATABASE_URL,
      },
      auth: {
        secret: process.env.NEXTAUTH_SECRET,
        url: process.env.NEXTAUTH_URL,
        kakao: {
          restApiKey: process.env.NEXT_PUBLIC_KAKAO_REST_API_KEY,
          clientSecret: process.env.KAKAO_CLIENT_SECRET,
        },
      },
      payment: {
        portone: {
          userCode: process.env.NEXT_PUBLIC_PORTONE_USER_CODE,
          apiKey: process.env.PORTONE_API_KEY,
          apiSecret: process.env.PORTONE_API_SECRET,
        },
      },
      kakao: {
        apiKey: process.env.NEXT_PUBLIC_KAKAO_MAP_API_KEY,
      },
      maps: {
        naver: {
          clientId: process.env.NEXT_PUBLIC_NAVER_MAP_CLIENT_ID,
        },
      },
    };

    try {
      return appConfigSchema.parse(rawConfig);
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.log(error);
        const errorMessages = error.issues

          .map(err => `${err.path.join('.')}: ${err.message}`)
          .join('\n');
        throw new Error(`Configuration validation failed:\n${errorMessages}`);
      }
      throw error;
    }
  }
}

export const configManager = ConfigManager.getInstance();

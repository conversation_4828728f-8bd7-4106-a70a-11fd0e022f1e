import { db } from '@/lib/db';
import { enrollments, payments, paymentEvents, type Enrollment, type NewPaymentEvent, type NewPayment } from '@/lib/db/schema';
import { classes, class_schedule_groups as classScheduleGroups } from '@/lib/db/schema';
import { eq, and, ne, sql } from 'drizzle-orm';
import { confirmTossPayment } from '@/lib/api/toss-payments';

interface CreateEnrollmentParams {
  memberId: string;
  classId: string;
  scheduleGroupId: number;
}

interface ConfirmPaymentParams {
  paymentKey: string;
  orderId: string;
  amount: number;
  memberId: string; // 권한 검증용
}

interface PreparePaymentWidget {
  clientKey: string;
  amount: number;
  orderId: string;
  orderName: string;
  customerName: string;
  successUrl: string;
  failUrl: string;
}

class BusinessRuleError extends Error {
  constructor(public code: string, message: string) {
    super(message);
    this.name = 'BusinessRuleError';
  }
}

/**
 * 수강신청 관련 비즈니스 로직 처리 (Drizzle Style)
 */
export const enrollmentService = {

  /**
   * 수강신청 + 결제 준비
   */
  async createEnrollment(params: CreateEnrollmentParams) {
    return await db.transaction(async (tx) => {
      // 1. 중복 신청 체크
      await this.checkDuplicateEnrollment(tx, params);

      // 2. 수업 정보 검증 및 조회
      const classInfo = await this.validateAndGetClassInfo(tx, params.classId, params.scheduleGroupId);

      // 3. 금액 계산
      const { totalAmount, depositAmount } = this.calculatePricing(classInfo);

      // 4. enrollment 생성
      const [enrollment] = await tx
        .insert(enrollments)
        .values({
          memberId: params.memberId,
          classId: params.classId,
          scheduleGroupId: params.scheduleGroupId,
          status: 'pending',
          totalAmount,
          depositAmount,
          enrollmentOrder: null, // 결제 성공 시 할당
        })
        .returning();

      // 5. 결제 위젯 정보 생성 (실제 payment는 결제 시점에 생성)
      const paymentWidget = await this.preparePaymentWidget(enrollment, classInfo, depositAmount);

      return {
        enrollment,
        paymentWidget
      };
    });
  },

  /**
   * 중복 신청 체크
   */
  async checkDuplicateEnrollment(tx: any, params: CreateEnrollmentParams) {
    const existing = await tx
      .select()
      .from(enrollments)
      .where(and(
        eq(enrollments.memberId, params.memberId),
        eq(enrollments.classId, params.classId),
        eq(enrollments.scheduleGroupId, params.scheduleGroupId),
        // cancelled 상태가 아닌 것만
        ne(enrollments.status, 'cancelled')
      ))
      .limit(1);

    if (existing.length > 0) {
      throw new BusinessRuleError('DUPLICATE_ENROLLMENT', '이미 해당 수업에 신청하셨습니다');
    }
  },

  /**
   * 수업 정보 검증 및 조회 (금액 정보 포함)
   */
  async validateAndGetClassInfo(tx: any, classId: string, scheduleGroupId: number) {
    // 수업 정보 조회 (금액 정보 포함)
    const [classInfo] = await tx
      .select({
        id: classes.id,
        title: classes.title,
        status: classes.status,
        pricePerSession: classes.price_per_session,
        sessionsPerWeek: classes.sessions_per_week,
        durationWeeks: classes.duration_weeks,
      })
      .from(classes)
      .where(eq(classes.id, classId))
      .limit(1);

    if (!classInfo) {
      throw new BusinessRuleError('CLASS_NOT_FOUND', '수업을 찾을 수 없습니다');
    }

    if (classInfo.status !== 'active') {
      throw new BusinessRuleError('CLASS_NOT_ACTIVE', '현재 신청할 수 없는 수업입니다');
    }

    // 스케줄 그룹 검증
    const [scheduleGroup] = await tx
      .select()
      .from(classScheduleGroups)
      .where(and(
        eq(classScheduleGroups.class_id, classId),
        eq(classScheduleGroups.id, scheduleGroupId)
      ))
      .limit(1);

    if (!scheduleGroup) {
      throw new BusinessRuleError('SCHEDULE_GROUP_NOT_FOUND', '수업 일정을 찾을 수 없습니다');
    }

    return classInfo;
  },

  /**
   * 금액 계산 (비즈니스 규칙 적용)
   */
  calculatePricing(classInfo: any) {
    // 총 세션 수 계산
    const totalSessions = classInfo.sessionsPerWeek * classInfo.durationWeeks;

    // 총 금액 계산
    const totalAmount = classInfo.pricePerSession * totalSessions;

    // 예약금 계산 (15% 고정)
    const depositAmount = Math.floor(totalAmount * 0.15);

    return {
      totalAmount,
      depositAmount,
      totalSessions,
    };
  },

  /**
   * 이벤트 로깅
   */
  async logEvent(tx: any, eventData: Omit<NewPaymentEvent, 'id' | 'createdAt'>) {
    await tx
      .insert(paymentEvents)
      .values({
        ...eventData,
        correlationId: crypto.randomUUID(),
      });
  },

  /**
   * 결제 승인 처리 (모든 비즈니스 로직 포함)
   */
  async confirmPayment(params: ConfirmPaymentParams) {
    const { paymentKey, orderId, amount, memberId } = params;

    return await db.transaction(async (tx) => {
      try {
        // enrollment 존재 및 권한 확인
        const enrollment = await this.validateEnrollmentOwnership(tx, orderId, memberId);

        // 결제 금액 일치성 확인
        this.validatePaymentAmount(enrollment, amount);

        // 중복 결제 확인
        await this.validateDuplicatePayment(tx, paymentKey);

        // 토스페이먼츠 승인 API 호출
        const tossResult = await confirmTossPayment({
          paymentKey,
          orderId,
          amount,
        });

        // enrollment_order 할당 (동시성 제어)
        const enrollmentOrder = await this.assignEnrollmentOrder(
          tx,
          enrollment.classId,
          enrollment.scheduleGroupId
        );

        // payment 레코드 생성
        const [payment] = await tx
          .insert(payments)
          .values({
            enrollmentId: enrollment.id,
            amount,
            paymentType: 'deposit',
            provider: 'tosspayments',
            externalPaymentKey: paymentKey,
            externalOrderId: orderId,
            paymentMethod: tossResult.method,
            paymentData: {
              method: tossResult.method,
              approvedAt: tossResult.approvedAt,
              totalAmount: tossResult.totalAmount,
            },
            status: 'paid',
            paidAt: new Date(),
          })
          .returning();

        // enrollment 상태 업데이트
        const [updatedEnrollment] = await tx
          .update(enrollments)
          .set({
            status: 'paid',
            enrollmentOrder,
            updatedAt: new Date(),
          })
          .where(eq(enrollments.id, orderId))
          .returning();

        // 이벤트 로깅
        await this.logEvent(tx, {
          paymentId: payment.id,
          enrollmentId: enrollment.id,
          eventType: 'payment_succeeded',
          eventData: {
            paymentKey,
            enrollmentOrder,
            amount,
            method: tossResult.method,
            memberId,
          },
        });

        return {
          enrollment: updatedEnrollment,
          payment,
          tossResult,
        };

      } catch (error) {
        // 비즈니스 룰 에러는 그대로 전파
        if (error instanceof BusinessRuleError) {
          throw error;
        }

        // 예상치 못한 오류 시 이벤트 로깅
        await this.logEvent(tx, {
          paymentId: '', // payment가 생성되지 않았으므로 빈 값
          enrollmentId: orderId,
          eventType: 'payment_failed',
          eventData: {
            paymentKey,
            amount,
            memberId,
            error: error instanceof Error ? error.message : 'Unknown error',
          },
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
        });

        throw new BusinessRuleError('PAYMENT_PROCESSING_FAILED', '결제 처리 중 오류가 발생했습니다');
      }
    });
  },

  /**
   * enrollment 존재 및 소유권 확인
   */
  async validateEnrollmentOwnership(tx: any, enrollmentId: string, memberId: string) {
    const [enrollment] = await tx
      .select()
      .from(enrollments)
      .where(
        eq(enrollments.id, enrollmentId),
        eq(enrollments.memberId, memberId),
        eq(enrollments.status, 'pending')
      )
      .limit(1);

    if (!enrollment) {
      throw new BusinessRuleError('ENROLLMENT_NOT_FOUND', '수강신청 정보를 찾을 수 없습니다');
    }

    return enrollment;
  },

  /**
   * 결제 금액 일치성 확인
   */
  validatePaymentAmount(enrollment: any, amount: number) {
    if (enrollment.depositAmount !== amount) {
      throw new BusinessRuleError('INVALID_PAYMENT_AMOUNT', '결제 금액이 일치하지 않습니다');
    }
  },

  /**
   * 중복 결제 확인
   */
  async validateDuplicatePayment(tx: any, paymentKey: string) {
    const existingPayment = await tx
      .select()
      .from(payments)
      .where(eq(payments.externalPaymentKey, paymentKey))
      .limit(1);

    if (existingPayment.length > 0) {
      throw new BusinessRuleError('ENROLLMENT_ALREADY_PROCESSED', '이미 처리된 결제입니다');
    }
  },

  /**
   * enrollment_order 할당 (동시성 제어)
   * TODO: for update를 사용했지만 여전히 동시성 문제가 발생할 수 있음
   */
  async assignEnrollmentOrder(tx: any, classId: string, scheduleGroupId: number): Promise<number> {
    // 동시성 제어를 위한 락 (같은 클래스+스케줄그룹의 paid 상태 enrollment들)
    await tx
      .select()
      .from(enrollments)
      .where(and(
        eq(enrollments.classId, classId),
        eq(enrollments.scheduleGroupId, scheduleGroupId),
        eq(enrollments.status, 'paid')
      ))
      .for('update');

    // 현재 최대 order 조회
    const [result] = await tx
      .select({
        maxOrder: sql<number>`COALESCE(MAX(${enrollments.enrollmentOrder}), 0)`
      })
      .from(enrollments)
      .where(and(
        eq(enrollments.classId, classId),
        eq(enrollments.scheduleGroupId, scheduleGroupId),
        eq(enrollments.status, 'paid')
      ));

    return result.maxOrder + 1;
  },

  /**
   * 결제 위젯 정보 준비
   */
  async preparePaymentWidget(
    enrollment: Enrollment,
    classInfo: any,
    depositAmount: number
  ): Promise<PreparePaymentWidget> {
    return {
      clientKey: process.env.NEXT_PUBLIC_TOSS_PAYMENTS_CLIENT_KEY!,
      amount: depositAmount,
      orderId: enrollment.id,
      orderName: `${classInfo.title} 수업 예약금`,
      customerName: "회원님", // 실제로는 member 정보에서 가져옴
      successUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/payments/success`,
      failUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/payments/fail`,
    };
  },
};
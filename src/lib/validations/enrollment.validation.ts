import { z } from 'zod';

// Enrollment 상태 enum
export const enrollmentStatusSchema = z.enum(['pending', 'paid', 'confirmed', 'cancelled', 'refunded']);
export const paymentStatusSchema = z.enum(['pending', 'paid', 'failed', 'cancelled', 'refunded']);

// API 요청 스키마
export const
  createEnrollmentRequestSchema = z.object({
  classId: z.uuid({ message: "올바른 UUID 형식이 아닙니다" }),
  scheduleGroupId: z.number().int().positive("양수여야 합니다")
});

// API 응답 스키마
export const createEnrollmentResponseSchema = z.object({
  enrollment: z.object({
    id: z.uuid(),
    status: enrollmentStatusSchema,
    totalAmount: z.number().int(),
    depositAmount: z.number().int(),
  }),
  paymentWidget: z.object({
    clientKey: z.string().min(1, "클라이언트 키가 필요합니다"),
    amount: z.number().int().positive("양수여야 합니다"),
    orderId: z.uuid({ message: "올바른 주문 ID가 아닙니다" }),
    orderName: z.string().min(1, "주문명이 필요합니다"),
    customerName: z.string().min(1, "고객명이 필요합니다"),
    successUrl: z.url({ message: "올바른 URL 형식이 아닙니다" }),
    failUrl: z.url({ message: "올바른 URL 형식이 아닙니다" })
  })
});

// 에러 응답 스키마
export const errorResponseSchema = z.object({
  error: z.string().min(1, "에러 코드가 필요합니다"),
  message: z.string().min(1, "에러 메시지가 필요합니다"),
  details: z.unknown().optional()
});

// 타입 추출
export type CreateEnrollmentRequest = z.infer<typeof createEnrollmentRequestSchema>;
export type CreateEnrollmentResponse = z.infer<typeof createEnrollmentResponseSchema>;
export type ErrorResponse = z.infer<typeof errorResponseSchema>;
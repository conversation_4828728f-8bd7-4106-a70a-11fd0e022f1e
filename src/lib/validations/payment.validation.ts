import { z } from 'zod';

// 결제 승인 요청 스키마 (extra 필드 허용)
export const confirmPaymentRequestSchema = z.object({
  paymentKey: z.string().min(1, 'Payment key is required'),
  orderId: z.uuid('올바른 주문 ID가 아닙니다'),
  amount: z.number().int().positive('Amount must be greater than 0'),
}); // strict() 없음으로 확장성 보장

// 결제 승인 성공 응답 스키마
export const confirmPaymentSuccessResponseSchema = z.object({
  success: z.literal(true),
  enrollment: z.object({
    id: z.uuid(),
    status: z.literal('paid'),
    enrollmentOrder: z.number().int().positive(),
    totalAmount: z.number().int().positive(),
    depositAmount: z.number().int().positive(),
    updatedAt: z.date(), // ISO string
  }),
  payment: z.object({
    id: z.string().uuid(),
    status: z.literal('paid'),
    amount: z.number().int().positive(),
    paymentMethod: z.string().min(1),
    externalPaymentKey: z.string().min(1),
    paidAt: z.date(), // ISO string
  }),
  tossResult: z.object({
    paymentKey: z.string(),
    orderId: z.string(),
    status: z.string(),
    totalAmount: z.number(),
    method: z.string(),
    approvedAt: z.string(),
  }),
});

// 결제 승인 에러 응답 스키마 (enrollments와 통일)
export const confirmPaymentErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string().min(1, '에러 코드가 필요합니다'),
  message: z.string().min(1, '에러 메시지가 필요합니다'),
  details: z.unknown().optional(), // 추가 에러 정보
});

// 토스페이먼츠 웹훅 스키마
export const tossWebhookSchema = z.object({
  eventType: z.string(),
  createdAt: z.string(),
  data: z.object({
    paymentKey: z.string(),
    orderId: z.string(),
    status: z.string(),
    totalAmount: z.number(),
    method: z.string().optional(),
    approvedAt: z.string().optional(),
    cancels: z.array(z.any()).optional(),
  }),
});

// 타입 추출
export type ConfirmPaymentRequest = z.infer<typeof confirmPaymentRequestSchema>;
export type ConfirmPaymentSuccessResponse = z.infer<typeof confirmPaymentSuccessResponseSchema>;
export type ConfirmPaymentErrorResponse = z.infer<typeof confirmPaymentErrorResponseSchema>;
export type ConfirmPaymentResponse = ConfirmPaymentSuccessResponse | ConfirmPaymentErrorResponse;
export type TossWebhook = z.infer<typeof tossWebhookSchema>;

// 비즈니스 룰 에러 코드
export const PaymentErrorCodes = {
  ENROLLMENT_NOT_FOUND: 'ENROLLMENT_NOT_FOUND',
  ENROLLMENT_ALREADY_PROCESSED: 'ENROLLMENT_ALREADY_PROCESSED',
  PAYMENT_PROCESSING_FAILED: 'PAYMENT_PROCESSING_FAILED',
  INVALID_PAYMENT_AMOUNT: 'INVALID_PAYMENT_AMOUNT',
  FORBIDDEN: 'FORBIDDEN',
  TOSS_PAYMENT_ERROR: 'TOSS_PAYMENT_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
} as const;

export type PaymentErrorCode = typeof PaymentErrorCodes[keyof typeof PaymentErrorCodes];
import { z } from 'zod';

// ============================================================================
// Core Payment Enums & Basic Types
// ============================================================================

export const PaymentMethodSchema = z.enum([
  'credit_card',
  'debit_card',
  'bank_transfer',
  'mobile_payment',
  'kakaopay',
  'naverpay',
  'payco',
]);
export type PaymentMethod = z.infer<typeof PaymentMethodSchema>;

export const PaymentStatusSchema = z.enum([
  'pending',
  'processing',
  'completed',
  'failed',
  'cancelled',
  'refunded',
  'partial_refund',
]);
export type PaymentStatus = z.infer<typeof PaymentStatusSchema>;

export const PaymentTypeSchema = z.enum([
  'deposit',
  'full_payment',
  'remaining_payment',
  'refund',
]);
export type PaymentType = z.infer<typeof PaymentTypeSchema>;

// ============================================================================
// Core Payment Data
// ============================================================================

export const PaymentDetailsSchema = z.object({
  id: z.uuid(),
  memberName: z.string(),
  className: z.string(),
  instructorName: z.string(),
  studioName: z.string(),
  scheduleGroupName: z.string(),
  paymentMethod: PaymentMethodSchema,
  paymentAmount: z.number().positive(),
  paymentType: PaymentTypeSchema,
  paymentDate: z.string(),
  paymentStatus: PaymentStatusSchema,
  transactionId: z.string().optional(),
  enrollmentId: z.uuid(),
  classTemplateId: z.uuid(),
  scheduleGroupId: z.uuid(),
});
export type PaymentDetails = z.infer<typeof PaymentDetailsSchema>;

// ============================================================================
// Next Steps Feature
// ============================================================================

export const NextStepSchema = z.object({
  title: z.string(),
  description: z.string(),
  completed: z.boolean().default(false),
});
export type NextStep = z.infer<typeof NextStepSchema>;

// ============================================================================
// Payment Success Flow
// ============================================================================

export const PaymentSuccessRequestSchema = z.object({
  enrollmentId: z.uuid(),
  transactionId: z.string().optional(),
});
export type PaymentSuccessRequest = z.infer<typeof PaymentSuccessRequestSchema>;

export const PaymentSuccessResponseSchema = z.object({
  success: z.boolean(),
  data: PaymentDetailsSchema,
});
export type PaymentSuccessResponse = z.infer<
  typeof PaymentSuccessResponseSchema
>;

export const PaymentSuccessErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  code: z.string().optional(),
});
export type PaymentSuccessErrorResponse = z.infer<
  typeof PaymentSuccessErrorResponseSchema
>;

export const PaymentSuccessDetailsSchema = PaymentDetailsSchema.extend({
  nextSteps: z.array(NextStepSchema),
  classStartDate: z.string().optional(),
  classEndDate: z.string().optional(),
  remainingAmount: z.number().optional(),
  totalAmount: z.number(),
});
export type PaymentSuccessDetails = z.infer<typeof PaymentSuccessDetailsSchema>;

export const PaymentSuccessFullResponseSchema = z.object({
  success: z.boolean(),
  data: PaymentSuccessDetailsSchema,
});
export type PaymentSuccessFullResponse = z.infer<
  typeof PaymentSuccessFullResponseSchema
>;

// ============================================================================
// Payment History Flow
// ============================================================================

export const PaymentHistoryRequestSchema = z.object({
  status: PaymentStatusSchema.optional(),
  paymentType: PaymentTypeSchema.optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  limit: z.number().positive().max(100).default(20),
  offset: z.number().min(0).default(0),
});
export type PaymentHistoryRequest = z.infer<typeof PaymentHistoryRequestSchema>;

export const PaymentHistoryResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    payments: z.array(PaymentDetailsSchema),
    total: z.number(),
    hasMore: z.boolean(),
  }),
});
export type PaymentHistoryResponse = z.infer<
  typeof PaymentHistoryResponseSchema
>;

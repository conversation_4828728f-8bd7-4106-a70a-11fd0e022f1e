import { NextRequest, NextResponse } from 'next/server';
import { requireMemberAuth } from '@/lib/auth/member.server';
import { enrollmentService } from '@/lib/services/enrollment.service';
import { 
  createEnrollmentRequestSchema, 
  createEnrollmentResponseSchema,
  type ErrorResponse 
} from '@/lib/validations/enrollment.validation';

/**
 * 수강신청 생성 API
 * POST /api/enrollments
 * 
 * @description
 * - 사용자가 특정 클래스와 스케줄 그룹에 대해 수강신청을 생성합니다
 * - 인증된 멤버만 접근 가능합니다
 * - 수강신청 생성과 동시에 결제 위젯 정보를 반환합니다
 * 
 * @example
 * // 요청 예시
 * POST /api/enrollments
 * Content-Type: application/json
 * Authorization: Bearer <access_token>
 * 
 * {
 *   "classId": "123e4567-e89b-12d3-a456-426614174000",
 *   "scheduleGroupId": 1
 * }
 * 
 * @example
 * // 성공 응답 (201)
 * {
 *   "enrollment": {
 *     "id": "987fcdeb-51a2-43d1-9f4e-123456789abc",
 *     "status": "pending",
 *     "totalAmount": 100000,
 *     "depositAmount": 10000
 *   },
 *   "paymentWidget": {
 *     "clientKey": "test_ck_docs_Ovk5rk1EwkEbP0W43n07xlzm",
 *     "amount": 10000,
 *     "orderId": "enrollment_987fcdeb-51a2-43d1-9f4e-123456789abc",
 *     "orderName": "요가 클래스 수강신청",
 *     "customerName": "홍길동",
 *     "successUrl": "https://example.com/payment/success",
 *     "failUrl": "https://example.com/payment/fail"
 *   }
 * }
 * 
 * @example
 * // 에러 응답 (400)
 * {
 *   "error": "VALIDATION_ERROR",
 *   "message": "입력값이 올바르지 않습니다",
 *   "details": [...]
 * }
 * 
 * @example
 * // 비즈니스 룰 에러 (400)
 * {
 *   "error": "CLASS_FULL",
 *   "message": "클래스 정원이 가득찼습니다"
 * }
 * 
 * @param {NextRequest} request - HTTP 요청 객체
 * @param {string} request.body.classId - 클래스 UUID (필수)
 * @param {number} request.body.scheduleGroupId - 스케줄 그룹 ID (필수, 양수)
 * 
 * @returns {Promise<NextResponse>} JSON 응답
 * @returns {CreateEnrollmentResponse} 201 - 수강신청 생성 성공
 * @returns {ErrorResponse} 400 - 요청 데이터 오류 또는 비즈니스 룰 위반
 * @returns {ErrorResponse} 401 - 인증 실패
 * @returns {ErrorResponse} 500 - 서버 내부 오류
 */
export async function POST(request: NextRequest) {
  try {
    // 1. 인증 확인
    const { member, errorResponse } = await requireMemberAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    // 2. 요청 데이터 검증
    const body = await request.json();
    const validationResult = createEnrollmentRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'VALIDATION_ERROR', 
          message: '입력값이 올바르지 않습니다',
          details: validationResult.error.issues
        } as ErrorResponse,
        { status: 400 }
      );
    }

    const { classId, scheduleGroupId } = validationResult.data;

    // 3. 수강신청 생성 (서비스에서 모든 비즈니스 로직 처리)
    const result = await enrollmentService.createEnrollment({
      memberId: member!.id,  // members 테이블의 실제 PK
      classId,
      scheduleGroupId,
    });

    // 4. 응답 데이터 포맷팅
    const response = createEnrollmentResponseSchema.parse({
      enrollment: {
        id: result.enrollment.id,
        status: result.enrollment.status,
        totalAmount: result.enrollment.totalAmount,
        depositAmount: result.enrollment.depositAmount,
      },
      paymentWidget: result.paymentWidget,
    });

    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    console.error('Enrollment creation error:', error);

    // 비즈니스 룰 에러 처리
    if (error instanceof Error && 'code' in error) {
      const businessError = error as any;
      return NextResponse.json(
        { 
          error: businessError.code, 
          message: businessError.message 
        } as ErrorResponse,
        { status: 400 }
      );
    }

    // 예상치 못한 에러
    return NextResponse.json(
      { 
        error: 'INTERNAL_SERVER_ERROR', 
        message: '서버 오류가 발생했습니다' 
      } as ErrorResponse,
      { status: 500 }
    );
  }
}
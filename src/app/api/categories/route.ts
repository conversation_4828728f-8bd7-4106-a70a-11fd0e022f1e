import { NextResponse } from 'next/server';
import { CLASS_CATEGORIES } from '@/lib/db/schema';

/**
 * 클래스 카테고리 목록 조회
 * 
 * @description 모든 클래스 카테고리 정보를 조회합니다.
 * 각 카테고리의 코드, 이모지, 제목, 설명을 포함합니다.
 * 
 * @returns {200} 카테고리 목록 조회 성공
 * @returns {500} 서버 내부 오류
 * 
 * @example
 * ```
 * GET /api/categories
 * 
 * Response:
 * {
 *   "data": [
 *     {
 *       "code": "fitness",
 *       "emoji": "💪",
 *       "title": "전신 기능성 운동",
 *       "description": "근력, 체력 강화"
 *     },
 *     {
 *       "code": "pilates",
 *       "emoji": "🤸‍♀️",
 *       "title": "필라테스",
 *       "description": "코어 안정성 강화"
 *     }
 *   ]
 * }
 * ```
 */
export async function GET() {
  try {
    return NextResponse.json({
      data: CLASS_CATEGORIES
    });
  } catch (error) {
    console.error('카테고리 목록 조회 오류:', error);

    return NextResponse.json(
      { message: '카테고리 목록 조회 중 오류가 발생했습니다.' },
      { status: 500 }
    );
  }
}
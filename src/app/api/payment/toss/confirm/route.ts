import { NextRequest, NextResponse } from 'next/server';
import { requireMemberAuth } from '@/lib/auth/member.server';
import { TossPaymentError } from '@/lib/api/toss-payments';
import { enrollmentService } from '@/lib/services/enrollment.service';
import { 
  confirmPaymentRequestSchema,
  confirmPaymentSuccessResponseSchema,
  PaymentErrorCodes,
  type ConfirmPaymentSuccessResponse,
  type ConfirmPaymentErrorResponse 
} from '@/lib/validations/payment.validation';

/**
 * 결제 승인 API
 * POST /api/payment/toss/confirm
 * 
 * @description
 * - 인증된 멤버의 결제 승인을 처리합니다
 * - 본인의 수강신청에 대해서만 결제 승인 가능합니다
 * - TossPayments API를 통해 실제 결제를 승인합니다
 */
export async function POST(request: NextRequest) {
  try {
    // 1. 인증 확인
    const { member, errorResponse } = await requireMemberAuth(request);
    if (errorResponse) {
      return errorResponse;
    }

    // 2. 요청 데이터 검증
    const body = await request.json();
    const validationResult = confirmPaymentRequestSchema.safeParse(body);

    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error);
      const errorResponse: ConfirmPaymentErrorResponse = {
        success: false,
        error: PaymentErrorCodes.VALIDATION_ERROR,
        message: '입력값이 올바르지 않습니다',
        details: validationResult.error.issues,
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    const { paymentKey, orderId, amount } = validationResult.data;

    console.log('Processing payment confirmation:', {
      memberId: member!.id,
      enrollmentId: orderId,
      paymentKey: paymentKey.substring(0, 10) + '...', // 보안을 위해 일부만
      amount,
      timestamp: new Date().toISOString()
    });

    // 3. 서비스 레이어를 통한 결제 승인 처리 (모든 비즈니스 로직 포함)
    const result = await enrollmentService.confirmPayment({
      paymentKey,
      orderId,
      amount,
      memberId: member!.id
    });

    console.log('Payment confirmed successfully:', {
      memberId: member!.id,
      enrollmentId: result.enrollment.id,
      paymentId: result.payment.id,
      enrollmentOrder: result.enrollment.enrollmentOrder,
      status: result.enrollment.status,
    });

    // 4. 응답 데이터 검증 및 포맷팅 (Zod 스키마 적용)
    const response: ConfirmPaymentSuccessResponse = confirmPaymentSuccessResponseSchema.parse({
      success: true,
      enrollment: {
        id: result.enrollment.id,
        status: 'paid',
        enrollmentOrder: result.enrollment.enrollmentOrder!,
        totalAmount: result.enrollment.totalAmount,
        depositAmount: result.enrollment.depositAmount,
        updatedAt: result.enrollment.updatedAt!.toISOString(),
      },
      payment: {
        id: result.payment.id,
        status: 'paid',
        amount: result.payment.amount,
        paymentMethod: result.payment.paymentMethod!,
        externalPaymentKey: result.payment.externalPaymentKey!,
        paidAt: result.payment.paidAt!.toISOString(),
      },
      tossResult: {
        paymentKey: result.tossResult.paymentKey,
        orderId: result.tossResult.orderId,
        status: result.tossResult.status,
        totalAmount: result.tossResult.totalAmount,
        method: result.tossResult.method,
        approvedAt: result.tossResult.approvedAt,
      },
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Payment confirmation error:', error);

    // 비즈니스 룰 에러 처리 (enrollments와 동일한 패턴)
    if (error instanceof Error && 'code' in error) {
      const businessError = error as any;
      const errorResponse: ConfirmPaymentErrorResponse = {
        success: false,
        error: businessError.code || PaymentErrorCodes.PAYMENT_PROCESSING_FAILED,
        message: businessError.message
      };
      return NextResponse.json(errorResponse, { status: 400 });
    }

    // 토스페이먼츠 API 에러
    if (error instanceof TossPaymentError) {
      const errorResponse: ConfirmPaymentErrorResponse = {
        success: false,
        error: PaymentErrorCodes.TOSS_PAYMENT_ERROR,
        message: error.message,
        details: { code: error.code, status: error.status }
      };
      return NextResponse.json(errorResponse, { status: error.status || 500 });
    }

    // 예상치 못한 에러 (enrollments와 동일)
    const errorResponse: ConfirmPaymentErrorResponse = {
      success: false,
      error: PaymentErrorCodes.INTERNAL_ERROR,
      message: '서버 오류가 발생했습니다'
    };
    return NextResponse.json(errorResponse, { status: 500 });
  }
}

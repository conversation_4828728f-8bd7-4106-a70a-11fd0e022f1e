import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Clock } from 'lucide-react';
import {
  Control,
  Controller,
  FieldErrors,
  UseFormWatch,
} from 'react-hook-form';
import { StudioFormData, EditStudioFormData } from '../_schemas/form.schema';

interface OperatingHoursProps {
  control: Control<StudioFormData | EditStudioFormData>;
  watch: UseFormWatch<StudioFormData | EditStudioFormData>;
  errors: FieldErrors<StudioFormData | EditStudioFormData>;
}

const DAYS = [
  { key: 'monday', label: '월요일' },
  { key: 'tuesday', label: '화요일' },
  { key: 'wednesday', label: '수요일' },
  { key: 'thursday', label: '목요일' },
  { key: 'friday', label: '금요일' },
  { key: 'saturday', label: '토요일' },
  { key: 'sunday', label: '일요일' },
] as const;

export default function OperatingHours({
  control,
  watch,
  errors,
}: OperatingHoursProps) {
  return (
    <div className=''>
      <h2 className='mb-4 text-base font-semibold'>운영시간 (선택)</h2>
      <div className='flex flex-col gap-4'>
        {DAYS.map(({ key, label }) => (
          <div key={key} className='flex items-center gap-3'>
            <div className='flex min-w-[60px] items-center gap-2'>
              <Clock size={16} className='text-gray-600' />
              <span className='text-sm font-medium'>{label}</span>
            </div>

            <div className='flex flex-1 items-center gap-2'>
              <Controller
                control={control}
                name={`operatingHours.${key}.closed`}
                render={({ field }) => (
                  <div className='flex items-center gap-2'>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      id={`${key}-closed`}
                    />
                    <label
                      htmlFor={`${key}-closed`}
                      className='cursor-pointer text-sm text-gray-600'
                    >
                      휴무
                    </label>
                  </div>
                )}
              />
              <>
                <Controller
                  control={control}
                  name={`operatingHours.${key}.open`}
                  render={({ field }) => (
                    <Input
                      type='time'
                      disabled={watch(`operatingHours.${key}.closed`)}
                      value={field.value}
                      onChange={field.onChange}
                      className='w-24'
                    />
                  )}
                />
                <span className='text-sm text-gray-500'>~</span>
                <Controller
                  control={control}
                  name={`operatingHours.${key}.close`}
                  render={({ field }) => (
                    <Input
                      type='time'
                      disabled={watch(`operatingHours.${key}.closed`)}
                      value={field.value}
                      onChange={field.onChange}
                      className='w-24'
                    />
                  )}
                />
              </>
              {/* {!watch(`operatingHours.${key}.closed`) && (
                <>
                  <Controller
                    control={control}
                    name={`operatingHours.${key}.open`}
                    render={({ field }) => (
                      <Input
                        type='time'
                        value={field.value}
                        onChange={field.onChange}
                        className='w-24'
                      />
                    )}
                  />
                  <span className='text-sm text-gray-500'>~</span>
                  <Controller
                    control={control}
                    name={`operatingHours.${key}.close`}
                    render={({ field }) => (
                      <Input
                        type='time'
                        value={field.value}
                        onChange={field.onChange}
                        className='w-24'
                      />
                    )}
                  />
                </>
              )} */}
            </div>
          </div>
        ))}

        {errors.operatingHours && (
          <p className='text-destructive mt-1 text-xs font-semibold'>
            {errors.operatingHours.message}
          </p>
        )}
      </div>
    </div>
  );
}

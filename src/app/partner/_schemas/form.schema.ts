import { CreateInstructorSchema } from '@/lib/schemas/instructor';
import { CreateStudioSchema } from '@/lib/schemas/studio';
import z from 'zod';

export const studioFormSchema = CreateStudioSchema.omit({
  partnerId: true,
  images: true,
  amenities: true,
  stationDistance: true,
  nearestStation: true,
  latitude: true,
  longitude: true,
  status: true,
  operatingHours: true,
}).extend({
  images: z
    .array(z.instanceof(File))
    .max(10, '이미지는 최대 10개까지 업로드 가능합니다')
    .optional(),
  phone: z.string().min(1, '전화번호를 입력해주세요'),
  links: z.object({
    website: z.union([z.url(), z.literal('')]),
    sns: z.union([z.url(), z.literal('')]),
  }),
  parking: z
    .object({
      type: z.enum(['free', 'paid']),
      price: z.number().optional(),
      description: z.string().optional(),
    })
    .optional(),
  shower: z
    .object({
      available: z.boolean(),
      description: z.string().optional(),
    })
    .optional(),
  workoutClothes: z
    .object({
      type: z.enum(['free', 'paid']),
      description: z.string().optional(),
    })
    .optional(),
  locker: z
    .object({
      type: z.enum(['free', 'paid']),
      price: z.number().optional(),
      description: z.string().optional(),
    })
    .optional(),
  operatingHours: z
    .object({
      monday: z.object({
        open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        closed: z.boolean(),
      }),
      tuesday: z.object({
        open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        closed: z.boolean(),
      }),
      wednesday: z.object({
        open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        closed: z.boolean(),
      }),
      thursday: z.object({
        open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        closed: z.boolean(),
      }),
      friday: z.object({
        open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        closed: z.boolean(),
      }),
      saturday: z.object({
        open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        closed: z.boolean(),
      }),
      sunday: z.object({
        open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
        closed: z.boolean(),
      }),
    })
    .optional(),
});

export type StudioFormData = z.infer<typeof studioFormSchema>;

// Edit용 스키마 - 모든 필드를 optional로 만듦
export const editStudioFormSchema = studioFormSchema.partial();
export type EditStudioFormData = z.infer<typeof editStudioFormSchema>;

export const instructorFormSchema = CreateInstructorSchema.omit({
  profileImages: true,
  links: true,
}).extend({
  links: z.object({
    website: z.union([z.url(), z.literal('')]),
    sns: z.union([z.url(), z.literal('')]),
  }),
  profileImages: z
    .array(z.instanceof(File))
    .max(3, '이미지는 최대 3개까지 업로드 가능합니다')
    .optional(),
});

export type InstructorFormData = z.infer<typeof instructorFormSchema>;

// TODO: create class form schema

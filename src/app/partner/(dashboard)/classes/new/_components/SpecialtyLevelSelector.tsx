'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { type Control } from 'react-hook-form';
import { CLASS_CATEGORIES, ClassLevelText } from '@/lib/db/schema';
import { CreateClassRequest } from '@/lib/api/partner/class.schema';

interface SpecialtyLevelSelectorProps {
  control: Control<CreateClassRequest>;
}

const categoryOptions = CLASS_CATEGORIES.map(category => ({
  value: category.code,
  emoji: category.emoji,
  label: category.title,
  description: category.description,
}));

// TODO: level -> label text mapper 생성
const levelLabel = {
  [ClassLevelText.BEGINNER]: '초급',
  [ClassLevelText.INTERMEDIATE]: '중급',
  [ClassLevelText.ADVANCED]: '고급',
};

const levelOptions = Object.values(ClassLevelText).map(value => ({
  value,
  label: levelLabel[value as keyof typeof levelLabel],
}));

export function SpecialtyLevelSelector({
  control,
}: SpecialtyLevelSelectorProps) {
  return (
    <div className='flex flex-col gap-4'>
      <FormField
        control={control}
        name='category'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              운동 분야 <span className='text-primary font-bold'>*</span>
            </FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='운동 분야' />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {categoryOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <span>{option.emoji}</span>
                      <div className="flex flex-col">
                        <span>{option.label}</span>
                        <span className="text-xs text-muted-foreground">{option.description}</span>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name='level'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              운동 수준 <span className='text-primary font-bold'>*</span>
            </FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='운동 수준' />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {levelOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

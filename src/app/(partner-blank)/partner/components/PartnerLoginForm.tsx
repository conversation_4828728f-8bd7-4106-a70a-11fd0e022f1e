'use client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUserStore } from '@/contexts/user.store';
import { loginPartner } from '@/lib/api/partner/auth';
import { cn } from '@/lib/utils';
import { PartnerLoginFormSchema } from '@/schemas/partner';
import {
  PartnerLoginFormData,
  PartnerLoginResponse,
  isPartnerLoginSuccess,
} from '@/types/partner';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import StatusMessage from './StatusMessage';

interface PartnerLoginFormProps {
  className?: string;
  onSuccess?: (response: PartnerLoginResponse) => void;
}

export const PartnerLoginForm: React.FC<PartnerLoginFormProps> = ({
  className,
  onSuccess,
}) => {
  const router = useRouter();
  const setRole = useUserStore(state => state.setRole);
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [partnerStatus, setPartnerStatus] = useState<
    'PENDING' | 'SUSPENDED' | 'REJECTED' | null
  >(null);

  const form = useForm<PartnerLoginFormData>({
    resolver: zodResolver(PartnerLoginFormSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const {
    watch,
    setValue,
    formState: { errors },
    handleSubmit: formHandleSubmit,
  } = form;
  const email = watch('email');
  const password = watch('password');

  const handleSubmit = async (data: PartnerLoginFormData) => {
    setIsLoading(true);
    setLoginError(null);
    setPartnerStatus(null);

    try {
      const response = await loginPartner(data);

      if (isPartnerLoginSuccess(response)) {
        // 로그인 성공
        toast.success(response.message);
        setRole('INSTRUCTOR');

        // 파트너 상태에 따른 처리
        const { status } = response.data.partner;

        if (status === 'ACTIVE') {
          // 활성화된 파트너는 대시보드로 리다이렉트 (기본값: /partner/dashboard)
          const redirectUrl = response.data.redirectUrl || '/partner/dashboard';
          router.push(redirectUrl);
        } else {
          // PENDING, SUSPENDED, REJECTED 상태는 상태 메시지 표시
          setPartnerStatus(status as 'PENDING' | 'SUSPENDED' | 'REJECTED');
        }

        // onSuccess 콜백 호출 (있는 경우)
        if (onSuccess) {
          onSuccess(response);
        }
      } else {
        // 로그인 실패
        setLoginError(response.message);
      }
    } catch (error) {
      console.error('Login error:', error);
      setLoginError('서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.');
    } finally {
      setIsLoading(false);
    }
  };

  if (partnerStatus) {
    return (
      <div className={cn('mx-auto w-full max-w-md', className)}>
        <StatusMessage status={partnerStatus} />
        <Button
          onClick={() => {
            setPartnerStatus(null);
            form.reset();
          }}
          variant='outline'
          className='mt-4 w-full'
        >
          다시 로그인
        </Button>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-1 flex-col', className)}>
      <form
        onSubmit={formHandleSubmit(handleSubmit)}
        className='flex flex-1 flex-col justify-between'
      >
        {/* 입력 필드들 */}
        <div className='flex flex-col gap-4'>
          {/* 이메일 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='email'
              className='text-sm font-medium text-gray-700'
            >
              이메일
            </Label>
            <Input
              id='email'
              type='email'
              value={email}
              onChange={e => setValue('email', e.target.value)}
              placeholder='<EMAIL>'
              disabled={isLoading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.email && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.email && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.email.message}
              </p>
            )}
          </div>

          {/* 비밀번호 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='password'
              className='text-sm font-medium text-gray-700'
            >
              비밀번호
            </Label>
            <Input
              id='password'
              type='password'
              value={password}
              onChange={e => setValue('password', e.target.value)}
              placeholder='비밀번호를 입력하세요'
              disabled={isLoading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.password && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.password && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.password.message}
              </p>
            )}
          </div>
          {loginError && (
            <div className='rounded-md bg-red-50 p-4'>
              <div className='flex'>
                <div className='flex-shrink-0'>
                  <svg
                    className='h-5 w-5 text-red-400'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
                    />
                  </svg>
                </div>
                <div className='ml-3'>
                  <p className='text-sm text-red-800'>{loginError}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 로그인 버튼 */}
        <div className='flex flex-col gap-2'>
          <Button
            type='submit'
            disabled={isLoading}
            className='gradient-bg h-10 w-full rounded-md font-semibold text-white transition-colors'
          >
            {isLoading ? (
              <div className='flex items-center justify-center'>
                <svg
                  className='mr-3 -ml-1 h-5 w-5 animate-spin text-white'
                  fill='none'
                  viewBox='0 0 24 24'
                >
                  <circle
                    className='opacity-25'
                    cx='12'
                    cy='12'
                    r='10'
                    stroke='currentColor'
                    strokeWidth='4'
                  />
                  <path
                    className='opacity-75'
                    fill='currentColor'
                    d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                  />
                </svg>
                로그인 중...
              </div>
            ) : (
              '로그인'
            )}
          </Button>

          {/* 회원가입 링크 */}
          <div className='text-center'>
            <span className='text-sm text-gray-600'>
              아직 계정이 없으신가요?{' '}
              <Link
                href='/partner/register'
                className='text-primary hover:text-primary/50 font-medium transition-colors'
              >
                파트너 가입하기
              </Link>
            </span>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PartnerLoginForm;

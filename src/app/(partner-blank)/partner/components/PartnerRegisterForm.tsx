import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { PartnerSignupFormSchema } from '@/schemas/partner';
import { PartnerSignupFormData } from '@/schemas/partner';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

interface PartnerRegisterFormProps {
  className?: string;
  onSubmit: (data: PartnerSignupFormData) => Promise<void>;
  loading?: boolean;
}

export const PartnerRegisterForm: React.FC<PartnerRegisterFormProps> = ({
  className,
  onSubmit,
  loading = false,
}) => {
  const [registerError, setRegisterError] = useState<string | null>(null);

  const form = useForm<PartnerSignupFormData>({
    resolver: zodResolver(PartnerSignupFormSchema),
    defaultValues: {
      email: '',
      password: '',
      passwordConfirm: '',
      contactInfo: {
        name: 'roys',
        phone: '',
      },
    },
  });

  const {
    watch,
    setValue,
    formState: { errors },
    handleSubmit: formHandleSubmit,
  } = form;

  const email = watch('email');
  const password = watch('password');
  const passwordConfirm = watch('passwordConfirm');
  const contactPhone = watch('contactInfo.phone');

  const handleSubmit = async (data: PartnerSignupFormData) => {
    setRegisterError(null);

    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Register error:', error);
      setRegisterError(
        '회원가입 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.'
      );
    }
  };

  return (
    <div className={cn('flex flex-1 flex-col', className)}>
      <form
        onSubmit={formHandleSubmit(handleSubmit, e => {
          console.log(e);
        })}
        className='flex flex-1 flex-col justify-between'
      >
        {/* 입력 필드들 */}
        <div className='flex flex-col gap-4'>
          {/* 이메일 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='email'
              className='text-sm font-medium text-gray-700'
            >
              이메일
            </Label>
            <Input
              id='email'
              type='email'
              value={email}
              onChange={e => setValue('email', e.target.value)}
              placeholder='<EMAIL>'
              disabled={loading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.email && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.email && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.email.message}
              </p>
            )}
          </div>

          {/* 비밀번호 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='password'
              className='text-sm font-medium text-gray-700'
            >
              비밀번호
            </Label>
            <Input
              id='password'
              type='password'
              value={password}
              onChange={e => setValue('password', e.target.value)}
              placeholder='비밀번호를 입력하세요'
              disabled={loading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.password && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.password && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.password.message}
              </p>
            )}
          </div>

          {/* 비밀번호 확인 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='passwordConfirm'
              className='text-sm font-medium text-gray-700'
            >
              비밀번호 확인
            </Label>
            <Input
              id='passwordConfirm'
              type='password'
              value={passwordConfirm}
              onChange={e => setValue('passwordConfirm', e.target.value)}
              placeholder='비밀번호를 다시 입력하세요'
              disabled={loading}
              required
              className={cn(
                'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                errors.passwordConfirm && 'border-red-500 focus:ring-red-500'
              )}
            />
            {errors.passwordConfirm && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.passwordConfirm.message}
              </p>
            )}
          </div>

          {/* 휴대폰 번호 입력 */}
          <div className='space-y-2'>
            <Label
              htmlFor='contactPhone'
              className='text-sm font-medium text-gray-700'
            >
              휴대폰 번호
            </Label>
            <div className='flex items-center gap-2'>
              <div className='flex-1'>
                <Input
                  id='contactPhone'
                  type='text'
                  placeholder='010-1234-5678'
                  value={contactPhone}
                  onChange={e => {
                    const { value } = e.target;
                    const formattedValue = value.replace(/[^0-9-]/g, '');
                    setValue('contactInfo.phone', formattedValue);
                  }}
                  maxLength={13}
                  disabled={loading}
                  required
                  className={cn(
                    'border-gray-300 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
                    errors.contactInfo?.phone &&
                      'border-red-500 focus:ring-red-500'
                  )}
                />
              </div>
            </div>
            {errors.contactInfo?.phone && (
              <p className='flex items-center gap-1 text-sm text-red-600'>
                <svg
                  className='h-4 w-4 flex-shrink-0'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                  />
                </svg>
                {errors.contactInfo.phone.message}
              </p>
            )}
          </div>

          {/* 에러 메시지 */}
          {registerError && (
            <div className='rounded-md bg-red-50 p-4'>
              <div className='flex'>
                <div className='flex-shrink-0'>
                  <svg
                    className='h-5 w-5 text-red-400'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z'
                    />
                  </svg>
                </div>
                <div className='ml-3'>
                  <p className='text-sm text-red-800'>{registerError}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 회원가입 버튼 */}
        <div className='flex flex-col gap-2'>
          <Button
            type='submit'
            disabled={loading}
            className='h-10 w-full rounded-lg bg-gradient-to-r from-[#5000D0] to-[#8645EF] font-semibold text-white transition-colors hover:from-[#5000D0]/80 hover:to-[#8645EF]/80'
          >
            {loading ? (
              <div className='flex items-center justify-center'>
                <svg
                  className='mr-3 -ml-1 h-5 w-5 animate-spin text-white'
                  fill='none'
                  viewBox='0 0 24 24'
                >
                  <circle
                    className='opacity-25'
                    cx='12'
                    cy='12'
                    r='10'
                    stroke='currentColor'
                    strokeWidth='4'
                  />
                  <path
                    className='opacity-75'
                    fill='currentColor'
                    d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'
                  />
                </svg>
                회원가입 중...
              </div>
            ) : (
              '파트너 가입하기'
            )}
          </Button>

          {/* 로그인 링크 */}
          <div className='text-center'>
            <span className='text-sm text-gray-600'>
              이미 계정이 있으신가요?{' '}
              <Link
                href='/partner/login'
                className='text-primary hover:text-primary/50 font-medium transition-colors'
              >
                로그인하기
              </Link>
            </span>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PartnerRegisterForm;

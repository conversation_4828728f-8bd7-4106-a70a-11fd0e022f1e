import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createServerClient, type CookieOptions } from '@supabase/ssr';

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const pathname = request.nextUrl.pathname;

  // 정적 파일과 API는 미들웨어 건너뛰기
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/auth') ||
    pathname === '/favicon.ico'
  ) {
    return response;
  }

  // 공개 경로들 - 인증 불필요
  const publicPaths = [
    '/',
    '/login',
    '/signup',
    '/onboarding',
    '/partner/login',
    '/partner/register',
    '/partner/pending',
    '/partner/suspended',
    '/unauthorized',
  ];

  // 공개 경로는 인증 체크 없이 통과
  if (publicPaths.includes(pathname)) {
    return response;
  }

  // Supabase 클라이언트 생성
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value,
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value,
            ...options,
          });
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({
            name,
            value: '',
            ...options,
          });
          response = NextResponse.next({
            request: {
              headers: request.headers,
            },
          });
          response.cookies.set({
            name,
            value: '',
            ...options,
          });
        },
      },
    }
  );

  try {
    // 사용자 인증 확인
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    // 인증되지 않은 사용자 처리
    if (error || !user) {
      // Partner 경로는 partner 로그인으로
      if (pathname.startsWith('/partner')) {
        return NextResponse.redirect(new URL('/partner/login', request.url));
      }
      // 일반 경로는 일반 로그인으로
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirectTo', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Partner 경로 권한 확인
    if (pathname.startsWith('/partner')) {
      const { data: partner } = await supabase
        .from('partners')
        .select('id, status')
        .eq('user_id', user.id)
        .single();

      if (!partner) {
        return NextResponse.redirect(new URL('/unauthorized', request.url));
      }

      // 파트너 상태별 리다이렉트 처리
      if (partner.status === 'PENDING') {
        // PENDING 상태는 pending 페이지로
        if (pathname !== '/partner/pending') {
          return NextResponse.redirect(
            new URL('/partner/pending', request.url)
          );
        }
      } else if (
        partner.status === 'SUSPENDED' ||
        partner.status === 'REJECTED'
      ) {
        // SUSPENDED, REJECTED 상태는 suspended 페이지로
        if (pathname !== '/partner/suspended') {
          return NextResponse.redirect(
            new URL('/partner/suspended', request.url)
          );
        }
      } else if (partner.status === 'ACTIVE') {
        // ACTIVE 상태만 다른 파트너 경로 접근 가능
        // 상태 페이지에 접근하려 하면 대시보드로 리다이렉트
        if (
          pathname === '/partner/pending' ||
          pathname === '/partner/suspended'
        ) {
          return NextResponse.redirect(
            new URL('/partner/dashboard', request.url)
          );
        }
      } else {
        // 알 수 없는 상태는 suspended 페이지로
        return NextResponse.redirect(
          new URL('/partner/suspended', request.url)
        );
      }
    }

    return response;
  } catch (error) {
    console.error('미들웨어 오류:', error);
    return response;
  }
}

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)'],
};

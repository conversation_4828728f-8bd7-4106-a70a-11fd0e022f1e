# 결제 시스템 Low Level Design

## 1. 데이터베이스 설계 (Drizzle ORM)

### 1.1 스키마 정의

#### enrollments 테이블
```typescript
import { pgTable, pgEnum, uuid, text, integer, timestamp, boolean, index, unique, json } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Enum 정의
export const enrollmentStatusEnum = pgEnum('enrollment_status', [
  'pending', 'paid', 'confirmed', 'cancelled', 'refunded'
]);

export const paymentTypeEnum = pgEnum('payment_type', [
  'deposit', 'remaining', 'refund'
]);

export const paymentStatusEnum = pgEnum('payment_status', [
  'pending', 'paid', 'failed', 'cancelled', 'refunded'
]);

export const paymentEventTypeEnum = pgEnum('payment_event_type', [
  'payment_requested', 
  'payment_succeeded',
  'payment_failed',
  'payment_cancelled',
  'refund_requested',
  'refund_completed',
  'webhook_received'
]);

export const enrollments = pgTable('enrollments', {
  id: uuid().primaryKey().defaultRandom(),
  memberId: uuid('member_id').notNull(),
  classId: uuid('class_id').notNull(),
  scheduleGroupId: integer('schedule_group_id').notNull(),
  
  // 상태 관리
  status: enrollmentStatusEnum().notNull().default('pending'),
  
  // 순서 관리 (선착순 확정을 위함)
  enrollmentOrder: integer('enrollment_order'),
  
  // 금액 정보
  totalAmount: integer('total_amount').notNull(),
  depositAmount: integer('deposit_amount').notNull(),
  
  // 메타데이터
  createdAt: timestamp('created_at', { precision: 6, withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { precision: 6, withTimezone: true }).$onUpdate(() => new Date()),
}, (table) => ({
  // 인덱스
  classOrderIdx: index('idx_enrollments_class_order').on(
    table.classId, 
    table.scheduleGroupId, 
    table.enrollmentOrder
  ),
  statusIdx: index('idx_enrollments_status').on(table.status),
  memberIdx: index('idx_enrollments_member').on(table.memberId),
  createdIdx: index('idx_enrollments_created').on(table.createdAt),
  updatedIdx: index('idx_enrollments_updated').on(table.updatedAt),
  
  // 유니크 제약
  uniqueMemberClass: unique('enrollments_unique_member_class').on(
    table.memberId, 
    table.classId, 
    table.scheduleGroupId
  ),
}));

// 타입 추출
export type Enrollment = typeof enrollments.$inferSelect;
export type NewEnrollment = typeof enrollments.$inferInsert;
export type EnrollmentUpdate = Partial<Pick<Enrollment, 'status' | 'enrollmentOrder' | 'updatedAt'>>;
```

#### payments 테이블
```typescript
// PG사별 추가 데이터 타입 정의
type PaymentData = {
  method?: string;
  cardType?: string;
  lastFourDigits?: string;
  issuerName?: string;
  acquirerName?: string;
  installmentPlanMonths?: number;
  approvedAt?: string;
  [key: string]: any;
};

export const payments = pgTable('payments', {
  id: uuid().primaryKey().defaultRandom(),
  enrollmentId: uuid('enrollment_id').notNull(),
  
  // 결제 정보
  amount: integer('amount').notNull(),
  paymentType: paymentTypeEnum().notNull().default('deposit'),
  
  // PG사 정보
  provider: text('provider').notNull().default('tosspayments'),
  externalPaymentKey: text('external_payment_key'),
  externalOrderId: text('external_order_id'),
  paymentMethod: text('payment_method'),
  
  // PG사별 추가 데이터 (JSON)
  paymentData: json().$type<PaymentData>(),
  
  // 상태
  status: paymentStatusEnum().notNull().default('pending'),
  
  // 타임스탬프
  createdAt: timestamp('created_at', { precision: 6, withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { precision: 6, withTimezone: true }).$onUpdate(() => new Date()),
  paidAt: timestamp('paid_at', { precision: 6, withTimezone: true }),
  failedAt: timestamp('failed_at', { precision: 6, withTimezone: true }),
  refundedAt: timestamp('refunded_at', { precision: 6, withTimezone: true }),
}, (table) => ({
  // 인덱스
  enrollmentIdx: index('idx_payments_enrollment').on(table.enrollmentId),
  providerKeyIdx: index('idx_payments_provider_key').on(table.provider, table.externalPaymentKey),
  statusIdx: index('idx_payments_status').on(table.status),
  createdIdx: index('idx_payments_created').on(table.createdAt),
  paidIdx: index('idx_payments_paid').on(table.paidAt),
  typeIdx: index('idx_payments_type').on(table.paymentType),
  
  // 유니크 제약
  uniqueExternalKey: unique('payments_unique_external_key').on(table.externalPaymentKey),
}));

// 타입 추출
export type Payment = typeof payments.$inferSelect;
export type NewPayment = typeof payments.$inferInsert;
export type PaymentUpdate = Partial<Pick<Payment, 'status' | 'externalPaymentKey' | 'paymentMethod' | 'paymentData' | 'paidAt' | 'failedAt' | 'refundedAt' | 'updatedAt'>>;
```

#### payment_events 테이블
```typescript
// 이벤트 데이터 타입 정의
type PaymentEventData = {
  amount?: number;
  paymentKey?: string;
  enrollmentOrder?: number;
  reason?: string;
  webhookType?: string;
  [key: string]: any;
};

export const paymentEvents = pgTable('payment_events', {
  id: uuid().primaryKey().defaultRandom(),
  paymentId: uuid('payment_id').notNull(),
  enrollmentId: uuid('enrollment_id').notNull(),
  
  // 이벤트 정보
  eventType: paymentEventTypeEnum().notNull(),
  
  // 이벤트 데이터 및 메타정보
  eventData: json().$type<PaymentEventData>(),
  errorMessage: text('error_message'),
  correlationId: uuid('correlation_id'),
  
  // 타임스탬프
  createdAt: timestamp('created_at', { precision: 6, withTimezone: true }).defaultNow(),
}, (table) => ({
  // 인덱스
  paymentIdx: index('idx_payment_events_payment').on(table.paymentId),
  enrollmentIdx: index('idx_payment_events_enrollment').on(table.enrollmentId),
  typeIdx: index('idx_payment_events_type').on(table.eventType),
  createdIdx: index('idx_payment_events_created').on(table.createdAt),
  correlationIdx: index('idx_payment_events_correlation').on(table.correlationId),
}));

// 타입 추출
export type PaymentEvent = typeof paymentEvents.$inferSelect;
export type NewPaymentEvent = typeof paymentEvents.$inferInsert;
```

### 1.2 Relations 정의
```typescript
export const enrollmentRelations = relations(enrollments, ({ one, many }) => ({
  // 단일 관계
  member: one(members, {
    fields: [enrollments.memberId],
    references: [members.id],
  }),
  class: one(classes, {
    fields: [enrollments.classId],
    references: [classes.id],
  }),
  scheduleGroup: one(classScheduleGroups, {
    fields: [enrollments.scheduleGroupId],
    references: [classScheduleGroups.id],
  }),
  
  // 다중 관계
  payments: many(payments),
  paymentEvents: many(paymentEvents),
}));

export const paymentRelations = relations(payments, ({ one, many }) => ({
  enrollment: one(enrollments, {
    fields: [payments.enrollmentId],
    references: [enrollments.id],
  }),
  events: many(paymentEvents),
}));

export const paymentEventRelations = relations(paymentEvents, ({ one }) => ({
  payment: one(payments, {
    fields: [paymentEvents.paymentId],
    references: [payments.id],
  }),
  enrollment: one(enrollments, {
    fields: [paymentEvents.enrollmentId],
    references: [enrollments.id],
  }),
}));
```

### 1.3 Drizzle-Zod 통합
```typescript
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import { z } from 'zod';

// 자동 생성된 기본 스키마
export const selectEnrollmentSchema = createSelectSchema(enrollments);
export const insertEnrollmentSchema = createInsertSchema(enrollments);
export const selectPaymentSchema = createSelectSchema(payments);
export const insertPaymentSchema = createInsertSchema(payments);
export const selectPaymentEventSchema = createSelectSchema(paymentEvents);
export const insertPaymentEventSchema = createInsertSchema(paymentEvents);

// 커스텀 검증 스키마
export const createEnrollmentSchema = insertEnrollmentSchema.pick({
  memberId: true,
  classId: true,
  scheduleGroupId: true,
  totalAmount: true,
  depositAmount: true,
}).extend({
  totalAmount: z.number().int().positive("양수여야 합니다"),
  depositAmount: z.number().int().positive("양수여야 합니다"),
}).refine((data) => data.depositAmount <= data.totalAmount, {
  message: "예약금은 총 금액보다 클 수 없습니다",
  path: ["depositAmount"],
});

export const updatePaymentSchema = insertPaymentSchema.pick({
  status: true,
  externalPaymentKey: true,
  paymentMethod: true,
  paymentData: true,
  paidAt: true,
  failedAt: true,
  refundedAt: true,
}).partial();

// Enum 검증 스키마 (pgEnum과 일치)
export const enrollmentStatusSchema = z.enum(['pending', 'paid', 'confirmed', 'cancelled', 'refunded']);
export const paymentStatusSchema = z.enum(['pending', 'paid', 'failed', 'cancelled', 'refunded']);
export const paymentEventTypeSchema = z.enum([
  'payment_requested', 
  'payment_succeeded',
  'payment_failed',
  'payment_cancelled',
  'refund_requested',
  'refund_completed',
  'webhook_received'
]);
```

## 2. API 명세 (Drizzle Style)

### 2.1 POST /api/enrollments (결제 준비)

#### 요청 스키마
```typescript
// Drizzle-Zod로 생성된 스키마 활용
const CreateEnrollmentRequestSchema = z.object({
  classId: z.string().uuid("올바른 UUID 형식이 아닙니다"),
  scheduleGroupId: z.number().int().positive("양수여야 합니다")
});

type CreateEnrollmentRequest = z.infer<typeof CreateEnrollmentRequestSchema>;
```

#### 응답 스키마
```typescript
const CreateEnrollmentResponseSchema = z.object({
  enrollment: selectEnrollmentSchema.pick({
    id: true,
    status: true,
    totalAmount: true,
    depositAmount: true,
    enrollmentOrder: true,
  }),
  paymentWidget: z.object({
    clientKey: z.string().min(1, "클라이언트 키가 필요합니다"),
    amount: z.number().int().positive("양수여야 합니다"),
    orderId: z.string().uuid("올바른 주문 ID가 아닙니다"),
    orderName: z.string().min(1, "주문명이 필요합니다"),
    customerName: z.string().min(1, "고객명이 필요합니다"),
    successUrl: z.string().url("올바른 URL 형식이 아닙니다"),
    failUrl: z.string().url("올바른 URL 형식이 아닙니다")
  })
});

type CreateEnrollmentResponse = z.infer<typeof CreateEnrollmentResponseSchema>;
```

### 2.2 POST /api/payments/confirm (결제 승인)

#### 요청 스키마
```typescript
const ConfirmPaymentRequestSchema = z.object({
  paymentKey: z.string().min(1, "결제 키가 필요합니다"),
  orderId: z.string().uuid("올바른 주문 ID가 아닙니다"),
  amount: z.number().int().positive("올바른 금액이 아닙니다")
});

type ConfirmPaymentRequest = z.infer<typeof ConfirmPaymentRequestSchema>;
```

#### 응답 스키마
```typescript
const ConfirmPaymentResponseSchema = z.object({
  success: z.literal(true),
  enrollment: selectEnrollmentSchema.pick({
    id: true,
    status: true,
    enrollmentOrder: true,
    totalAmount: true,
    depositAmount: true,
  }),
  payment: selectPaymentSchema.pick({
    id: true,
    status: true,
    amount: true,
    paymentMethod: true,
    paidAt: true,
  }).extend({
    paidAt: z.date().nullable().transform((date) => date?.toISOString() ?? null),
  })
});

type ConfirmPaymentResponse = z.infer<typeof ConfirmPaymentResponseSchema>;
```

### 2.3 POST /api/partner/classes/{classId}/schedules/{scheduleGroupId}/confirm (수업 확정)

#### 요청 스키마
```typescript
const ConfirmClassRequestSchema = z.object({
  maxCapacity: z.number().int().positive("정원은 1명 이상이어야 합니다"),
  reason: z.string().max(200, "200자 이하로 입력해주세요").optional()
});

type ConfirmClassRequest = z.infer<typeof ConfirmClassRequestSchema>;
```

#### 응답 스키마
```typescript
const ConfirmClassResponseSchema = z.object({
  confirmed: z.array(z.object({
    enrollmentId: z.string().uuid(),
    memberId: z.string().uuid(),
    memberName: z.string().min(1),
    enrollmentOrder: z.number().int().positive()
  })),
  refunded: z.array(z.object({
    enrollmentId: z.string().uuid(),
    memberId: z.string().uuid(),
    memberName: z.string().min(1),
    enrollmentOrder: z.number().int().positive(),
    refundAmount: z.number().int().positive()
  })),
  summary: z.object({
    totalEnrollments: z.number().int().nonnegative(),
    confirmedCount: z.number().int().nonnegative(),
    refundedCount: z.number().int().nonnegative()
  })
});

type ConfirmClassResponse = z.infer<typeof ConfirmClassResponseSchema>;
```

### 2.4 에러 응답 스키마
```typescript
const ErrorResponseSchema = z.object({
  error: z.string().min(1, "에러 코드가 필요합니다"),
  message: z.string().min(1, "에러 메시지가 필요합니다"),
  details: z.unknown().optional()
});

const ValidationErrorResponseSchema = z.object({
  error: z.literal('VALIDATION_ERROR'),
  message: z.string().min(1, "검증 에러 메시지가 필요합니다"),
  details: z.array(z.object({
    field: z.string().min(1, "필드명이 필요합니다"),
    message: z.string().min(1, "검증 에러 메시지가 필요합니다")
  }))
});

type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
type ValidationErrorResponse = z.infer<typeof ValidationErrorResponseSchema>;
```

## 3. 서비스 레이어 구현 (Drizzle Style)

### 3.1 EnrollmentService
```typescript
import { db } from '@/lib/db';
import { enrollments, payments, paymentEvents } from '@/lib/db/schema';
import { eq, and, desc, count, sql, ne, inArray } from 'drizzle-orm';
import type { NewEnrollment, NewPayment, NewPaymentEvent } from '@/lib/db/schema';

interface CreateEnrollmentParams {
  memberId: string;
  classId: string;
  scheduleGroupId: number;
  totalAmount: number;
  depositAmount: number;
}

/**
 * 수강신청 관련 비즈니스 로직 처리 (Drizzle Style)
 */
export const enrollmentService = {

  /**
   * 수강신청 + 결제 준비
   */
  async createEnrollment(params: CreateEnrollmentParams) {
    return await db.transaction(async (tx) => {
      // 1. 중복 신청 체크
      await this.checkDuplicateEnrollment(tx, params);
      
      // 2. 수업 정보 검증
      const classInfo = await this.validateClass(tx, params.classId, params.scheduleGroupId);
      
      // 3. enrollment 생성
      const [enrollment] = await tx
        .insert(enrollments)
        .values({
          memberId: params.memberId,
          classId: params.classId,
          scheduleGroupId: params.scheduleGroupId,
          status: 'pending',
          totalAmount: params.totalAmount,
          depositAmount: params.depositAmount,
          enrollmentOrder: null, // 결제 성공 시 할당
        })
        .returning();
      
      // 4. 이벤트 로깅
      await this.logEvent(tx, {
        paymentId: '', // payment는 결제 시점에 생성되므로 빈 값
        enrollmentId: enrollment.id,
        eventType: 'payment_prepared',
        eventData: { amount: params.depositAmount },
      });
      
      // 5. 결제 위젯 정보 생성
      const paymentWidget = await this.preparePaymentWidget(enrollment, classInfo);
      
      return {
        enrollment,
        paymentWidget
      };
    });
  },

  /**
   * 결제 승인 처리
   */
  async confirmPayment(paymentKey: string, orderId: string, amount: number) {
    return await db.transaction(async (tx) => {
      try {
        // 1. TossPayments 승인 API 호출
        const tossResult = await this.callTossPaymentsAPI(paymentKey, orderId, amount);
        
        // 2. enrollment_order 할당 (동시성 제어)
        const enrollmentOrder = await this.assignEnrollmentOrder(
          tx, 
          orderId // enrollmentId
        );
        
        // 3. payment 레코드 생성 (정확한 결제 정보로)
        const [createdPayment] = await tx
          .insert(payments)
          .values({
            enrollmentId: orderId,
            amount: amount,
            paymentType: 'deposit',
            provider: 'tosspayments',
            externalPaymentKey: paymentKey,
            externalOrderId: orderId,
            paymentMethod: tossResult.method,
            paymentData: tossResult,
            status: 'paid',
            paidAt: new Date(),
          })
          .returning();
        
        // 4. enrollment 업데이트
        const [updatedEnrollment] = await tx
          .update(enrollments)
          .set({
            status: 'paid',
            enrollmentOrder,
            updatedAt: new Date(),
          })
          .where(eq(enrollments.id, orderId))
          .returning();
        
        // 5. 이벤트 로깅
        await this.logEvent(tx, {
          paymentId: createdPayment.id,
          enrollmentId: updatedEnrollment.id,
          eventType: 'payment_succeeded',
          eventData: { paymentKey, enrollmentOrder },
        });
        
        return {
          enrollment: updatedEnrollment,
          payment: createdPayment
        };
        
      } catch (error) {
        // 보상 트랜잭션 처리
        await this.handlePaymentConfirmFailure(paymentKey, error);
        throw error;
      }
    });
  },

  /**
   * 중복 신청 체크
   */
  async checkDuplicateEnrollment(tx: any, params: CreateEnrollmentParams) {
    const existing = await tx
      .select()
      .from(enrollments)
      .where(and(
        eq(enrollments.memberId, params.memberId),
        eq(enrollments.classId, params.classId),
        eq(enrollments.scheduleGroupId, params.scheduleGroupId),
        // cancelled 상태가 아닌 것만
        ne(enrollments.status, 'cancelled')
      ))
      .limit(1);

    if (existing.length > 0) {
      throw new BusinessRuleError('DUPLICATE_ENROLLMENT', '이미 해당 수업에 신청하셨습니다');
    }
  },

  /**
   * enrollment_order 할당 (동시성 제어)
   */
  async assignEnrollmentOrder(tx: any, enrollmentId: string): Promise<number> {
    // 해당 enrollment의 클래스 정보 조회
    const [enrollmentInfo] = await tx
      .select({
        classId: enrollments.classId,
        scheduleGroupId: enrollments.scheduleGroupId,
      })
      .from(enrollments)
      .where(eq(enrollments.id, enrollmentId))
      .limit(1);

    // 동시성 제어를 위한 락
    await tx
      .select()
      .from(enrollments)
      .where(and(
        eq(enrollments.classId, enrollmentInfo.classId),
        eq(enrollments.scheduleGroupId, enrollmentInfo.scheduleGroupId),
        eq(enrollments.status, 'paid')
      ))
      .for('update');

    // 현재 최대 order 조회
    const [result] = await tx
      .select({ 
        maxOrder: sql<number>`COALESCE(MAX(${enrollments.enrollmentOrder}), 0)` 
      })
      .from(enrollments)
      .where(and(
        eq(enrollments.classId, enrollmentInfo.classId),
        eq(enrollments.scheduleGroupId, enrollmentInfo.scheduleGroupId),
        eq(enrollments.status, 'paid')
      ));

    return result.maxOrder + 1;
  },

  /**
   * 이벤트 로깅
   */
  async logEvent(tx: any, eventData: NewPaymentEvent) {
    await tx
      .insert(paymentEvents)
      .values({
        ...eventData,
        correlationId: crypto.randomUUID(),
      });
  },

  /**
   * 결제 위젯 정보 준비
   */
  async preparePaymentWidget(enrollment: any, classInfo: any) {
    return {
      clientKey: process.env.NEXT_PUBLIC_TOSS_PAYMENTS_CLIENT_KEY!,
      amount: enrollment.depositAmount,
      orderId: enrollment.id,
      orderName: `${classInfo.title} 수업 예약금`,
      customerName: "회원님", // 실제로는 member 정보에서 가져옴
      successUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/payments/success`,
      failUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/payments/fail`,
    };
  },
};
```

### 3.2 Payment Gateway 구현
```typescript
interface PaymentGateway {
  confirmPayment(params: ConfirmPaymentParams): Promise<PaymentResult>;
  cancelPayment(params: CancelPaymentParams): Promise<CancelResult>;
  verifyWebhook(signature: string, payload: string): boolean;
}

export class TossPaymentsGateway implements PaymentGateway {
  constructor(
    private config: {
      secretKey: string;
      webhookSecret: string;
    }
  ) {}

  async confirmPayment(params: ConfirmPaymentParams): Promise<PaymentResult> {
    const response = await fetch('https://api.tosspayments.com/v1/payments/confirm', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(this.config.secretKey + ':').toString('base64')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new TossPaymentsError(error.code, error.message);
    }

    return await response.json();
  }

  async cancelPayment(params: CancelPaymentParams): Promise<CancelResult> {
    const response = await fetch(`https://api.tosspayments.com/v1/payments/${params.paymentKey}/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(this.config.secretKey + ':').toString('base64')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        cancelReason: params.cancelReason
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new TossPaymentsError(error.code, error.message);
    }

    return await response.json();
  }

  verifyWebhook(signature: string, payload: string): boolean {
    const expectedSignature = crypto
      .createHmac('sha256', this.config.webhookSecret)
      .update(payload)
      .digest('base64');

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  }
}
```

### 3.3 Partner Service Layer
```typescript
import { db } from '@/lib/db';
import { enrollments, payments, paymentEvents, classes } from '@/lib/db/schema';
import { eq, and, inArray } from 'drizzle-orm';
import type { AuthenticatedPartner } from '@/lib/auth/partner.server';

/**
 * 파트너 관련 수업 관리 서비스 (Drizzle Style)
 */
export const partnerClassManagementService = {

  /**
   * 수업 확정 처리 (파트너 권한 필요)
   */
  async confirmClass(
    partner: AuthenticatedPartner,
    classId: string, 
    scheduleGroupId: number, 
    maxCapacity: number
  ) {
    // 1. 파트너 권한 검증 (해당 클래스의 소유자인지 확인)
    await this.validateClassOwnership(partner.id, classId);
    
    return await db.transaction(async (tx) => {
      // 2. 결제 완료된 신청자들을 순서대로 조회
      const paidEnrollments = await tx
        .select({
          id: enrollments.id,
          memberId: enrollments.memberId,
          enrollmentOrder: enrollments.enrollmentOrder,
          depositAmount: enrollments.depositAmount,
          // member 정보도 조인으로 가져올 수 있음
        })
        .from(enrollments)
        .where(and(
          eq(enrollments.classId, classId),
          eq(enrollments.scheduleGroupId, scheduleGroupId),
          eq(enrollments.status, 'paid')
        ))
        .orderBy(enrollments.enrollmentOrder);

      const confirmed = paidEnrollments.slice(0, maxCapacity);
      const toRefund = paidEnrollments.slice(maxCapacity);

      // 3. 확정자 상태 업데이트
      if (confirmed.length > 0) {
        await tx
          .update(enrollments)
          .set({ 
            status: 'confirmed',
            updatedAt: new Date() 
          })
          .where(inArray(enrollments.id, confirmed.map(e => e.id)));
      }

      // 4. 초과분 환불 처리
      for (const enrollment of toRefund) {
        await this.processRefund(tx, enrollment.id, '정원 초과로 인한 자동 환불');
      }

      return {
        confirmed: confirmed.map(e => ({
          enrollmentId: e.id,
          memberId: e.memberId,
          memberName: "회원명", // 실제로는 조인된 member 정보
          enrollmentOrder: e.enrollmentOrder!,
        })),
        refunded: toRefund.map(e => ({
          enrollmentId: e.id,
          memberId: e.memberId,
          memberName: "회원명",
          enrollmentOrder: e.enrollmentOrder!,
          refundAmount: e.depositAmount,
        })),
        summary: {
          totalEnrollments: paidEnrollments.length,
          confirmedCount: confirmed.length,
          refundedCount: toRefund.length,
        }
      };
    });
  },

  /**
   * 환불 처리 (내부 헬퍼 메서드)
   */
  async processRefund(tx: any, enrollmentId: string, reason: string) {
    // 1. enrollment 상태 업데이트
    await tx
      .update(enrollments)
      .set({ 
        status: 'refunded',
        updatedAt: new Date() 
      })
      .where(eq(enrollments.id, enrollmentId));

    // 2. payment 상태 업데이트
    await tx
      .update(payments)
      .set({ 
        status: 'refunded',
        refundedAt: new Date(),
        updatedAt: new Date() 
      })
      .where(eq(payments.enrollmentId, enrollmentId));

    // 3. 이벤트 로깅
    await tx
      .insert(paymentEvents)
      .values({
        paymentId: '', // 실제로는 payment id 조회 필요
        enrollmentId,
        eventType: 'refund_completed',
        eventData: { reason },
        correlationId: crypto.randomUUID(),
      });
  },

  /**
   * 클래스 소유권 검증
   */
  async validateClassOwnership(partnerId: string, classId: string): Promise<void> {
    // 실제 구현에서는 classes 테이블과 조인하여 partner_id 확인
    // 현재는 예시로 간단하게 구현
    const [classData] = await db
      .select({ partnerId: classes.partnerId })
      .from(classes)
      .where(eq(classes.id, classId))
      .limit(1);
    
    if (!classData || classData.partnerId !== partnerId) {
      throw new Error('해당 클래스에 대한 권한이 없습니다');
    }
  },
};
```

## 4. 마이그레이션 관리

### 4.1 Drizzle Config
```typescript
// drizzle.config.ts
import { defineConfig } from 'drizzle-kit';

export default defineConfig({
  dialect: 'postgresql',
  schema: './src/lib/db/schema/*',
  out: './drizzle',
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
  verbose: true,
  strict: true,
});
```

### 4.2 마이그레이션 예시
```sql
-- 0001_payment_system.sql
CREATE TABLE IF NOT EXISTS "enrollments" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "member_id" uuid NOT NULL,
  "class_id" uuid NOT NULL,
  "schedule_group_id" integer NOT NULL,
  "status" text DEFAULT 'pending' NOT NULL,
  "enrollment_order" integer,
  "total_amount" integer NOT NULL,
  "deposit_amount" integer NOT NULL,
  "created_at" timestamp with time zone DEFAULT now(),
  "updated_at" timestamp with time zone DEFAULT now()
);

CREATE INDEX IF NOT EXISTS "idx_enrollments_class_order" ON "enrollments" ("class_id","schedule_group_id","enrollment_order");
CREATE INDEX IF NOT EXISTS "idx_enrollments_status" ON "enrollments" ("status");
CREATE UNIQUE INDEX IF NOT EXISTS "enrollments_unique_member_class" ON "enrollments" ("member_id","class_id","schedule_group_id");
```

## 5. 테스트 전략 (Drizzle)

### 5.1 단위 테스트
```typescript
import { describe, it, expect, beforeEach } from 'vitest';
import { db } from '@/lib/db';
import { enrollments, payments } from '@/lib/db/schema';
import { enrollmentService } from '@/lib/services/enrollment.service';

describe('EnrollmentService (Drizzle)', () => {
  beforeEach(async () => {
    // 테스트 데이터 정리
    await db.delete(payments);
    await db.delete(enrollments);
  });

  it('should create enrollment with payment', async () => {
    // Given
    const params = {
      memberId: 'test-member-id',
      classId: 'test-class-id',
      scheduleGroupId: 1,
      totalAmount: 200000,
      depositAmount: 30000,
    };

    // When
    const result = await enrollmentService.createEnrollment(params);

    // Then
    expect(result.enrollment.status).toBe('pending');
    expect(result.payment.status).toBe('pending');
    expect(result.payment.amount).toBe(30000);

    // DB 검증
    const savedEnrollment = await db.query.enrollments.findFirst({
      where: (enrollments, { eq }) => eq(enrollments.id, result.enrollment.id),
    });
    expect(savedEnrollment).toBeDefined();
    expect(savedEnrollment!.status).toBe('pending');
  });

  it('should assign enrollment order on payment confirmation', async () => {
    // Given: 기존 결제 완료된 enrollment 생성
    const [existingEnrollment] = await db.insert(enrollments).values({
      memberId: 'existing-member',
      classId: 'test-class-id',
      scheduleGroupId: 1,
      status: 'paid',
      enrollmentOrder: 1,
      totalAmount: 200000,
      depositAmount: 30000,
    }).returning();

    // 새로운 enrollment 생성
    const { enrollment } = await enrollmentService.createEnrollment({
      memberId: 'new-member',
      classId: 'test-class-id',
      scheduleGroupId: 1,
      totalAmount: 200000,
      depositAmount: 30000,
    });

    // When: 결제 승인
    const result = await enrollmentService.confirmPayment(
      'test-payment-key',
      enrollment.id,
      30000
    );

    // Then: 순서가 2번으로 할당되어야 함
    expect(result.enrollment.enrollmentOrder).toBe(2);
  });
});
```

### 5.2 통합 테스트 
```typescript
describe('Payment Integration (Drizzle)', () => {
  it('should handle complete payment flow with database', async () => {
    // 1. Create enrollment
    const enrollment = await enrollmentService.createEnrollment({
      memberId: 'test-user',
      classId: 'test-class',
      scheduleGroupId: 1,
      totalAmount: 200000,
      depositAmount: 30000,
    });

    // 2. Verify database state
    const enrollmentInDb = await db.query.enrollments.findFirst({
      where: (enrollments, { eq }) => eq(enrollments.id, enrollment.enrollment.id),
      with: {
        payments: true,
        paymentEvents: true,
      },
    });

    expect(enrollmentInDb).toBeDefined();
    expect(enrollmentInDb!.payments).toHaveLength(1);
    expect(enrollmentInDb!.paymentEvents).toHaveLength(1);

    // 3. Confirm payment
    const confirmed = await enrollmentService.confirmPayment(
      'test-payment-key',
      enrollment.enrollment.id,
      30000
    );

    // 4. Verify final state
    const finalState = await db.query.enrollments.findFirst({
      where: (enrollments, { eq }) => eq(enrollments.id, enrollment.enrollment.id),
      with: {
        payments: true,
        paymentEvents: true,
      },
    });

    expect(finalState!.status).toBe('paid');
    expect(finalState!.enrollmentOrder).toBe(1);
    expect(finalState!.payments[0].status).toBe('paid');
    expect(finalState!.paymentEvents).toHaveLength(2); // prepared + succeeded
  });
});
```

이 Drizzle 스타일의 LLD는 타입 안전성, 성능, 개발자 경험을 모두 고려한 현대적인 접근법을 제공합니다.
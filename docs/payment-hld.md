# 결제 시스템 High Level Design

## 1. 비즈니스 요구사항

### 1.1 핵심 정책
- **정원 초과 허용**: 수업 정원이 차더라도 결제 및 수강신청 가능
- **결제 완료 = 수강신청 완료**: 결제가 성공해야만 수강신청이 성립됨
- **선착순 기반 확정**: 수업 확정 시 결제 완료 순서대로 선착순 처리
- **자동 환불**: 정원 초과분은 수업 확정 시 자동 환불 처리

### 1.2 사용자 플로우
1. 사용자가 수업 선택 후 "수강신청" 클릭
2. 즉시 결제 위젯 표시 (수강신청 레코드 미리 생성)
3. 결제 완료 시 수강신청 확정
4. 결제 실패 시 수강신청 무효

### 1.3 비즈니스 규칙
- **예약금 결제**: 전체 수업료의 15%만 선결제
- **100% 환불**: 수업 확정 전까지 언제든 취소 가능
- **순서 보장**: 결제 완료 시간 기준으로 enrollment_order 부여
- **투명성**: 사용자는 자신의 대기 순서를 알 수 있음

## 2. 설계 원칙

### 2.1 Prepare 전략 채택
**결정**: 결제 전에 enrollment 레코드를 미리 생성하고, payment는 실제 결제 시점에 정확한 정보로 생성

**이유**:
- **명확한 추적성**: enrollmentId를 orderId로 사용하여 1:1 매핑
- **TossPayments 호환성**: orderId 요구사항 충족
- **정확한 데이터 저장**: payment_type, paymentMethod 등은 실제 결제 시점에서만 확정 가능
- **데이터 무결성**: 불확실한 정보를 미리 저장하지 않아 완전성 보장
- **중복 결제 방지**: enrollment 레코드 존재 여부로 간단히 체크

### 2.2 브라우저 중심 결제 처리
**결정**: 서버는 결제 승인만 담당, 실제 결제는 브라우저에서 처리

**이유**:
- **보안**: 카드 정보가 우리 서버를 거치지 않음
- **UX**: TossPayments의 검증된 결제 위젯 활용
- **다양한 결제수단**: 카드, 계좌이체, 간편결제 등 통합 지원

### 2.3 상태 관리 단순화
**결정**: pending → paid → confirmed/refunded 3단계 상태

**이유**:
- **명확성**: 각 상태의 의미가 명확함
- **단순성**: 복잡한 중간 상태 제거
- **실용성**: 비즈니스 요구사항에 최적화

## 3. 시스템 아키텍처

### 3.1 전체 구조
```
┌─────────────┐     ┌──────────────┐     ┌─────────────┐
│   Browser   │────▶│   API Layer  │────▶│   Service   │
│             │     │              │     │    Layer    │
│ TossPayments│     │              │     │             │
│   Widget    │     │              │     │             │
└─────┬───────┘     └──────────────┘     └──────┬──────┘
      │                                         │
      │                                         │
      │            ┌─────────────────────────────▼─────┐
      │            │     Payment Gateway              │
      │            │      (Abstract)                  │
      │            └─────────────────┬─────────────────┘
      │                              │
      │            ┌─────────────────▼─────────────────┐
      └───────────▶│      TossPayments API            │
                   │     (External Service)           │
                   └──────────────────────────────────┘
```

### 3.2 컴포넌트 역할

#### Browser + TossPayments Widget
- 결제 UI 렌더링
- 사용자 카드 정보 입력 처리
- TossPayments와 직접 통신
- 결제 결과를 서버로 전달

#### API Layer
- 결제 준비 엔드포인트 제공
- 결제 승인 엔드포인트 제공
- 웹훅 수신 처리
- 인증 및 권한 검증

#### Service Layer
- 비즈니스 로직 처리
- 트랜잭션 관리
- 상태 전이 제어
- 장애 복구 처리

#### Payment Gateway (Abstract)
- PG사 독립적인 인터페이스 제공
- TossPayments 구현체로 실제 처리
- 향후 다른 PG사 확장 가능

## 4. 결제 플로우

### 4.1 전체 프로세스
```
[수강신청 요청] → [Prepare] → [결제 위젯] → [결제 처리] → [승인] → [완료]
```

### 4.2 단계별 상세 플로우

#### 1단계: 결제 위젯 렌더링 (Prepare)
```
사용자 "수강신청" 클릭
         ↓
POST /api/enrollments
         ↓
enrollment 생성 (status: 'pending')
         ↓
결제 위젯 정보 준비 (clientKey, amount, orderId 등)
         ↓
브라우저에 TossPayments 위젯 렌더링
```

#### 2단계: 결제 요청 (브라우저 → TossPayments)
```
사용자가 위젯에서 카드 정보 입력
         ↓
브라우저가 TossPayments API 직접 호출
         ↓
TossPayments에서 결제 처리
         ↓
paymentKey 생성 및 결제 상태 관리
```

#### 3단계: 결제 정보 전달 (브라우저 → 서버)
```
TossPayments 결제 성공/실패
         ↓
successUrl 또는 failUrl로 리다이렉트
         ↓
브라우저에서 서버로 결제 결과 전달
(paymentKey, orderId, amount)
```

#### 4단계: 결제 승인 (서버 → TossPayments)
```
POST /api/payments/confirm 호출
         ↓
TossPayments 결제 승인 API 호출
         ↓
승인 성공 시:
- payment 레코드 생성 (정확한 결제 정보로)
- enrollment 상태 업데이트 (paid)
- enrollment_order 할당
         ↓
사용자에게 완료 응답
```

### 4.3 상태 전이도
```
enrollment 상태:
[PENDING] ──결제 성공──▶ [PAID] ──수업 확정──▶ [CONFIRMED]
    │                       │
    │                       └──정원 초과──▶ [REFUNDED]
    │
    └──결제 실패──▶ [CANCELLED]

payment 상태:
[PENDING] ──결제 성공──▶ [PAID]
    │
    └──결제 실패──▶ [FAILED]
```

## 5. 주요 시나리오

### 5.1 정상 시나리오: 정원 내 수강신청
```
1. 사용자 A가 정원 10명 수업에 5번째로 신청
2. 결제 완료 → enrollment_order: 5
3. 관리자가 수업 확정 → A는 confirmed 상태
4. 수업 참여 가능
```

### 5.2 정원 초과 시나리오
```
1. 정원 10명 수업에 15명이 신청 및 결제 완료
2. enrollment_order: 1~15 할당됨
3. 관리자가 수업 확정
4. 1~10번: confirmed 상태 (수업 참여)
5. 11~15번: 자동 환불 처리 → refunded 상태
```

### 5.3 결제 실패 시나리오
```
1. 사용자가 수강신청 → enrollment 생성 (pending)
2. 결제 위젯에서 카드 한도 초과로 결제 실패
3. failUrl로 리다이렉트
4. enrollment: cancelled 상태로 변경
5. 사용자에게 실패 안내 + 재시도 옵션
```

## 6. 장애 대응 전략

### 6.1 주요 실패 시나리오

#### 시나리오 1: 결제 성공 후 시스템 오류
**상황**: TossPayments 결제는 성공했으나 서버 DB 업데이트 실패

**대응**:
1. 자동 환불 시도
2. 환불 성공 시 사용자에게 안내
3. 환불 실패 시 수동 처리 큐 등록
4. CS팀 알림 발송

#### 시나리오 2: 네트워크 분할
**상황**: TossPayments API 호출 타임아웃

**대응**:
1. 지수 백오프 재시도 (최대 3회)
2. 재시도 실패 시 수동 처리 큐 등록
3. 사용자에게 처리 지연 안내

#### 시나리오 3: 동시성 문제
**상황**: 다수 사용자가 동시에 마지막 자리 신청

**대응**:
1. enrollment_order 할당 시 DB 락 사용
2. 순서대로 처리되도록 보장
3. 정확한 선착순 유지

### 6.2 복구 전략

#### 자동 복구
- 네트워크 일시 장애
- 재시도 가능한 API 오류
- 간헐적 DB 연결 오류

#### 반자동 복구
- 결제 성공 후 시스템 오류 (자동 환불)
- PG사 웹훅 지연 (상태 동기화)

#### 수동 복구
- 환불 API 실패
- 복잡한 데이터 불일치
- 예상치 못한 PG사 오류

## 7. 모니터링 및 운영

### 7.1 핵심 메트릭
- **결제 성공률**: 전체 결제 시도 대비 성공률
- **결제 포기율**: 위젯 표시 후 미완료율
- **평균 처리 시간**: 수강신청부터 완료까지 소요 시간
- **환불 처리율**: 자동 환불 성공률
- **API 응답 시간**: 각 엔드포인트별 성능

### 7.2 알림 기준
- 결제 성공률 < 95%
- 환불 처리 실패 발생
- API 응답 시간 > 3초
- 수동 처리 큐 적체 (> 10건)

### 7.3 로그 전략
- **구조화된 로그**: 검색 및 분석 용이
- **상관관계 ID**: 전체 플로우 추적 가능
- **민감정보 마스킹**: 개인정보 보호
- **이벤트 기반**: 상태 변화마다 로그 기록

## 8. 보안 고려사항

### 8.1 데이터 보안
- **카드 정보 비저장**: 모든 민감 정보는 TossPayments에서 처리
- **PCI DSS 불필요**: 우리 시스템은 카드 정보를 다루지 않음
- **개인정보 최소화**: 결제에 필요한 최소한의 정보만 저장

### 8.2 통신 보안
- **HTTPS 강제**: 모든 통신은 암호화
- **웹훅 서명 검증**: HMAC-SHA256으로 위변조 방지
- **API 키 관리**: 환경변수로 안전하게 관리

### 8.3 접근 제어
- **인증 필수**: 모든 결제 API는 로그인 필요
- **권한 검증**: 본인의 수강신청만 처리 가능
- **관리자 기능**: 수업 확정은 관리자만 가능
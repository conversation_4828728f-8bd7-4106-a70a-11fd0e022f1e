### Enrollments API 테스트 (수강신청 및 결제 시스템)

# 변수 설정
@baseUrl = http://localhost:3000
@contentType = application/json
@projectRef = dxodiiizyfzpueyvoaqr

# 회원 인증을 위한 변수 설정
# 실제 테스트 시에는 브라우저에서 로그인 후 개발자 도구의 Application > Cookies에서
# sb-{{projectRef}}-auth-token 쿠키 값을 복사해서 사용하세요
@memberAuthToken = base64-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

# 분할된 쿠키 방식 (브라우저에서 .0, .1 쿠키 값을 별도로 복사)
@memberAuthToken0 = base64-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
@memberAuthToken1 = W1hZ2VzL2RlZmF1bHRfcHJvZmlsZS5qcGVnIiwiZW1haWwiOiJvaG5vbm9ub0BuYXRlLmNvbSIsImVtYWlsX3ZlcmlmaWVkIjp0cnVlLCJmdWxsX25hbWUiOiLsnbTsp4Dtm4giLCJpc3MiOiJodHRwczovL2thcGkua2FrYW8uY29tIiwibmFtZSI6IuydtOyngO2biCIsInBob25lX3ZlcmlmaWVkIjpmYWxzZSwicHJlZmVycmVkX3VzZXJuYW1lIjoi7J207KeA7ZuIIiwicHJvdmlkZXJfaWQiOiI0MzQ0NjMzNTU1Iiwic3ViIjoiNDM0NDYzMzU1NSIsInVzZXJfbmFtZSI6IuydtOyngO2biCJ9LCJwcm92aWRlciI6Imtha2FvIiwibGFzdF9zaWduX2luX2F0IjoiMjAyNS0wOC0wMVQxMjo1ODoxNy4yMDkxNzVaIiwiY3JlYXRlZF9hdCI6IjIwMjUtMDgtMDFUMTI6NTg6MTcuMjA5MjI4WiIsInVwZGF0ZWRfYXQiOiIyMDI1LTA4LTAyVDEyOjMwOjI3LjkxMzQ4NVoiLCJlbWFpbCI6Im9obm9ub25vQG5hdGUuY29tIn1dLCJjcmVhdGVkX2F0IjoiMjAyNS0wOC0wMVQxMjo1ODoxNy4xOTAyNzZaIiwidXBkYXRlZF9hdCI6IjIwMjUtMDgtMDJUMTI6MzA6MjkuNDM1NDU1WiIsImlzX2Fub255bW91cyI6ZmFsc2V9LCJwcm92aWRlcl90b2tlbiI6Imhna19qd3hteGs2THBJY0poR2ZGWGNHLUJpMGg3dHBHQUFBQUFRb1hFTzhBQUFHWWFzTWFGOHdoS0NwRnNVSlIiLCJwcm92aWRlcl9yZWZyZXNoX3Rva2VuIjoiZjJCV2lHSlRxWU5VcS1nQzFIUGgwLWdGWjE4Mk1DVm5BQUFBQWdvWEVPOEFBQUdZYXNNYUVzd2hLQ3BGc1VKUiJ9

# 파트너 인증을 위한 변수 설정 (파트너 전용 API용)
@partnerAuthToken = ...

# 테스트용 클래스 및 스케줄 그룹 ID (실제 데이터로 변경 필요)
@validClassId = a54820fc-bf7e-48f7-aa50-d0044facea38
@validScheduleGroupId = 1
@invalidClassId = 00000000-0000-0000-0000-000000000000
@invalidUuid = invalid-uuid-format

###############################################################################
# 인증 테스트 가이드
###############################################################################
# 
# 1. 브라우저에서 http://localhost:3000/login 접속
# 2. 회원 계정으로 로그인 (카카오 로그인 등)
# 3. 개발자 도구 (F12) 열기
# 4. Application > Storage > Cookies > localhost:3000
# 5. sb-{{projectRef}}-auth-token 쿠키 값 복사
# 6. 위의 @authToken 변수에 붙여넣기
# 7. 테스트 실행
#
# 주의사항:
# - 토큰은 보통 1시간 후 만료됩니다
# - 회원만 수강신청이 가능합니다
# - 동일한 클래스에 중복 신청할 수 없습니다
# - 클래스 상태가 'active'여야 신청 가능합니다
###############################################################################

###############################################################################
# 📋 수강신청 API 스펙 (POST /api/enrollments)
###############################################################################
#
# 🎯 사용 시점: 사용자가 클래스 상세 페이지에서 결제 방법을 선택하여 "수강신청" 버튼 클릭 시
#
# 📥 요청 데이터:
# {
#   "classId": "UUID",     // 클래스 템플릿 ID (UUID 형식)
#   "scheduleGroupId": "UUID",     // 스케줄 그룹 ID (UUID 형식)
#   "paymentMethod": "string",     // 결제 방법: credit_card, kakaopay, naverpay, etc.
#   "paymentType": "string"        // 결제 타입: full_payment, deposit
# }
#
# 📤 성공 응답 (201 Created):
# {
#   "success": true,
#   "data": {
#     "enrollmentId": "UUID",      // 생성된 수강신청 ID
#     "paymentMethod": "string",   // 선택한 결제 방법
#     "paymentType": "string",     // 결제 타입
#     "amount": number,            // 결제할 금액
#     "redirectUrl": "string"      // 결제 페이지 리다이렉트 URL (선택사항)
#   }
# }
#
# 🚨 에러 응답:
# - 400 Bad Request: 입력값 오류, 잘못된 enum 값
# - 401 Unauthorized: 인증되지 않은 사용자
# - 404 Not Found: 존재하지 않는 클래스/스케줄 그룹
# - 409 Conflict: 중복 수강신청, 정원 초과
# - 422 Unprocessable Entity: 잘못된 UUID 형식
#
###############################################################################

###############################################################################
# 📋 결제 성공 처리 API 스펙 (POST /api/payments/success)
###############################################################################
#
# 🎯 사용 시점: 외부 결제 서비스(카카오페이, 토스페이먼츠 등)에서 결제 완료 후 콜백
#
# 📥 요청 데이터:
# {
#   "enrollmentId": "UUID",        // 수강신청 ID
#   "transactionId": "string"      // 결제 거래 ID (선택사항)
# }
#
# 📤 성공 응답 (200 OK):
# {
#   "success": true,
#   "data": {
#     "id": "UUID",                    // 결제 정보 ID
#     "memberName": "string",          // 회원명
#     "className": "string",           // 클래스명
#     "instructorName": "string",      // 강사명
#     "studioName": "string",          // 스튜디오명
#     "scheduleGroupName": "string",   // 스케줄 그룹명
#     "paymentMethod": "string",       // 결제 방법
#     "paymentAmount": number,         // 결제 금액
#     "paymentType": "string",         // 결제 타입
#     "paymentDate": "string",         // 결제 일시
#     "paymentStatus": "string",       // 결제 상태
#     "transactionId": "string",       // 거래 ID
#     "enrollmentId": "UUID",          // 수강신청 ID
#     "classId": "UUID",       // 클래스 템플릿 ID
#     "scheduleGroupId": "UUID",       // 스케줄 그룹 ID
#     "nextSteps": [                   // 다음 단계 안내
#       {
#         "title": "string",
#         "description": "string",
#         "completed": boolean
#       }
#     ],
#     "classStartDate": "string",      // 수업 시작일 (선택사항)
#     "classEndDate": "string",        // 수업 종료일 (선택사항)
#     "remainingAmount": number,       // 잔여 금액 (선택사항)
#     "totalAmount": number            // 총 금액
#   }
# }
#
###############################################################################

###############################################################################
# 📋 결제 내역 조회 API 스펙 (GET /api/payments/history)
###############################################################################
#
# 🎯 사용 시점: 회원이 마이페이지에서 결제 내역을 확인할 때
#
# 📥 쿼리 파라미터:
# - status: PaymentStatus (선택사항) - completed, pending, failed, etc.
# - paymentType: PaymentType (선택사항) - full_payment, deposit, remaining_payment
# - startDate: string (선택사항) - YYYY-MM-DD 형식
# - endDate: string (선택사항) - YYYY-MM-DD 형식
# - limit: number (선택사항) - 1~100, 기본값 20
# - offset: number (선택사항) - 0 이상, 기본값 0
#
# 📤 성공 응답 (200 OK):
# {
#   "success": true,
#   "data": {
#     "payments": [...],           // 결제 정보 배열
#     "total": number,             // 전체 결제 건수
#     "hasMore": boolean           // 추가 데이터 존재 여부
#   }
# }
#
###############################################################################

###############################################################################
# 성공 케이스 테스트
###############################################################################

###
# ✅ 수강신청 성공 - 전액 결제
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# ✅ 수강신청 성공 - 예약금 결제
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": "550e8400-e29b-41d4-a716-446655440002",
  "paymentMethod": "kakaopay",
  "paymentType": "deposit"
}

###
# ✅ 다른 클래스에 수강신청 - 네이버페이
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "456e7890-e89b-12d3-a456-426614174001",
  "scheduleGroupId": "550e8400-e29b-41d4-a716-446655440003",
  "paymentMethod": "naverpay",
  "paymentType": "full_payment"
}

###############################################################################
# 인증 관련 에러 케이스
###############################################################################

###
# 🔴 인증 없이 수강신청 시도 (401 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# 🔴 잘못된 토큰으로 수강신청 시도 (401 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token=invalid-token-value

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###############################################################################
# 유효성 검증 에러 케이스
###############################################################################

###
# 🔴 잘못된 UUID 형식의 classId (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{invalidUuid}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# 🔴 음수 scheduleGroupId (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": "invalid-negative-id"
}

###
# 🔴 0인 scheduleGroupId (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": "00000000-0000-0000-0000-000000000000"
}

###
# 🔴 소수점 scheduleGroupId (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": 1.5
}

###
# 🔴 문자열 scheduleGroupId (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": "invalid"
}

###
# 🔴 필수 필드 누락 - classId 없음 (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# 🔴 필수 필드 누락 - scheduleGroupId 없음 (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}"
}

###
# 🔴 빈 객체 전송 (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{}

###
# 🔴 빈 문자열 classId (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# 🔴 null 값 전송 (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": null,
  "scheduleGroupId": null
}

###############################################################################
# 비즈니스 로직 에러 케이스
###############################################################################

###
# 🔴 존재하지 않는 클래스 ID (400 에러 - CLASS_NOT_FOUND)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{invalidClassId}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# 🔴 존재하지 않는 스케줄 그룹 ID (400 에러 - SCHEDULE_GROUP_NOT_FOUND)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": 99999
}

###
# 🔴 중복 수강신청 (400 에러 - DUPLICATE_ENROLLMENT)
# 주의: 이 테스트는 위의 성공 케이스 실행 후에 실행해야 함
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# 🔴 비활성화된 클래스에 신청 (400 에러 - CLASS_NOT_ACTIVE)
# 주의: 실제 테스트를 위해서는 비활성화된 클래스 ID가 필요함
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "inactive-class-id",
  "scheduleGroupId": 1
}

###############################################################################
# Content-Type 관련 에러 케이스
###############################################################################

###
# 🔴 잘못된 Content-Type (415 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: text/plain
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# 🔴 Content-Type 누락 (400 에러)
POST {{baseUrl}}/api/enrollments
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# 🔴 잘못된 JSON 형식 (400 에러)
POST {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment",
}

###############################################################################
# HTTP 메서드 에러 케이스
###############################################################################

###
# 🔴 지원하지 않는 GET 메서드 (405 에러)
GET {{baseUrl}}/api/enrollments
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

###
# 🔴 지원하지 않는 PUT 메서드 (405 에러)
PUT {{baseUrl}}/api/enrollments
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "classId": "{{validClassId}}",
  "scheduleGroupId": {{validScheduleGroupId}},
  "paymentMethod": "credit_card",
  "paymentType": "full_payment"
}

###
# 🔴 지원하지 않는 DELETE 메서드 (405 에러)
DELETE {{baseUrl}}/api/enrollments
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

###############################################################################
# 프론트엔드 구현 가이드
###############################################################################
#
# 🎯 React/Next.js 구현 예시:
#
# ```typescript
# async function handleEnrollment(classId: string, scheduleGroupId: number) {
#   try {
#     // 로딩 상태 시작
#     setIsLoading(true);
#     
#     const response = await fetch('/api/enrollments', {
#       method: 'POST',
#       headers: { 'Content-Type': 'application/json' },
#       body: JSON.stringify({ classId, scheduleGroupId })
#     });
# 
#     if (!response.ok) {
#       const error = await response.json();
#       throw new Error(error.message);
#     }
# 
#     const { enrollment, payment, paymentWidget } = await response.json();
#     
#     // 결제 위젯 초기화
#     const tossPayments = TossPayments(paymentWidget.clientKey);
#     await tossPayments.requestPayment('카드', {
#       amount: paymentWidget.amount,
#       orderId: paymentWidget.orderId,
#       orderName: paymentWidget.orderName,
#       customerName: paymentWidget.customerName,
#       successUrl: paymentWidget.successUrl,
#       failUrl: paymentWidget.failUrl,
#     });
#     
#   } catch (error) {
#     // 에러 처리
#     alert(error.message);
#   } finally {
#     setIsLoading(false);
#   }
# }
# ```
#
# 🔄 상태 관리:
# - enrollment.status: 'pending' → 'paid' → 'confirmed'
# - payment.status: 'pending' → 'paid'
# - 결제 완료 후 enrollment.enrollmentOrder 할당됨
#
# 💰 금액 계산:
# - totalAmount: 총 수강료 (pricePerSession × sessionsPerWeek × durationWeeks)
# - depositAmount: 예약금 (totalAmount × 15%)
# - remainingAmount: 잔여 금액 (totalAmount - depositAmount)
#
# 🔐 인증 처리:
# - 로그인하지 않은 사용자: 로그인 페이지로 리다이렉트
# - 토큰 만료: 재로그인 요청
# - 권한 없음: 적절한 에러 메시지 표시
#
###############################################################################

###############################################################################
# 결제 성공 처리 API 테스트 (POST /api/payments/success)
###############################################################################

###
# 🔴 결제 성공 처리 - 인증 없음 (401 에러)
POST {{baseUrl}}/api/payments/success
Content-Type: {{contentType}}

{
  "enrollmentId": "550e8400-e29b-41d4-a716-************",
  "transactionId": "tx_1234567890"
}

###
# ✅ 결제 성공 처리 (성공)
POST {{baseUrl}}/api/payments/success
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "enrollmentId": "550e8400-e29b-41d4-a716-************",
  "transactionId": "tx_1234567890"
}

###
# ✅ 결제 성공 처리 - transactionId 없음 (성공)
POST {{baseUrl}}/api/payments/success
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "enrollmentId": "550e8400-e29b-41d4-a716-************"
}

###
# 🔴 잘못된 enrollmentId UUID (422 에러)
POST {{baseUrl}}/api/payments/success
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "enrollmentId": "invalid-uuid",
  "transactionId": "tx_1234567890"
}

###
# 🔴 존재하지 않는 enrollmentId (404 에러)
POST {{baseUrl}}/api/payments/success
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "enrollmentId": "00000000-0000-0000-0000-000000000000",
  "transactionId": "tx_1234567890"
}

###############################################################################
# 결제 내역 조회 API 테스트 (GET /api/payments/history)
###############################################################################

###
# 🔴 결제 내역 조회 - 인증 없음 (401 에러)
GET {{baseUrl}}/api/payments/history
Accept: {{contentType}}

###
# ✅ 결제 내역 조회 - 기본 (성공)
GET {{baseUrl}}/api/payments/history
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

###
# ✅ 결제 내역 조회 - 상태 필터 (성공)
GET {{baseUrl}}/api/payments/history?status=completed
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

###
# ✅ 결제 내역 조회 - 결제 타입 필터 (성공)
GET {{baseUrl}}/api/payments/history?paymentType=full_payment
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

###
# ✅ 결제 내역 조회 - 날짜 범위 필터 (성공)
GET {{baseUrl}}/api/payments/history?startDate=2024-01-01&endDate=2024-12-31
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

###
# ✅ 결제 내역 조회 - 페이징 (성공)
GET {{baseUrl}}/api/payments/history?limit=10&offset=0
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

###
# 🔴 잘못된 status 값 (400 에러)
GET {{baseUrl}}/api/payments/history?status=invalid_status
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

###
# 🔴 잘못된 limit 값 (400 에러)
GET {{baseUrl}}/api/payments/history?limit=101
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

###############################################################################
# 잔액 결제 API 테스트 (POST /api/payments/remaining)
###############################################################################

###
# 🔴 잔액 결제 - 인증 없음 (401 에러)
POST {{baseUrl}}/api/payments/remaining
Content-Type: {{contentType}}

{
  "enrollmentId": "550e8400-e29b-41d4-a716-************",
  "paymentMethod": "credit_card"
}

###
# ✅ 잔액 결제 - 신용카드 (성공)
POST {{baseUrl}}/api/payments/remaining
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "enrollmentId": "550e8400-e29b-41d4-a716-************",
  "paymentMethod": "credit_card"
}

###
# ✅ 잔액 결제 - 카카오페이 (성공)
POST {{baseUrl}}/api/payments/remaining
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "enrollmentId": "550e8400-e29b-41d4-a716-************",
  "paymentMethod": "kakaopay"
}

###
# 🔴 잘못된 enrollmentId UUID (422 에러)
POST {{baseUrl}}/api/payments/remaining
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "enrollmentId": "invalid-uuid",
  "paymentMethod": "credit_card"
}

###
# 🔴 존재하지 않는 enrollmentId (404 에러)
POST {{baseUrl}}/api/payments/remaining
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token.0={{memberAuthToken0}}; sb-{{projectRef}}-auth-token.1={{memberAuthToken1}}

{
  "enrollmentId": "00000000-0000-0000-0000-000000000000",
  "paymentMethod": "credit_card"
}

###############################################################################
# 파트너 전용 API 테스트
###############################################################################

###
# 🔴 파트너 수강신청 목록 - 인증 없음 (401 에러)
GET {{baseUrl}}/api/partner/enrollments
Accept: {{contentType}}

###
# ✅ 파트너 수강신청 목록 조회 - 기본 (성공)
GET {{baseUrl}}/api/partner/enrollments
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{partnerAuthToken}}

###
# ✅ 파트너 수강신청 목록 - 스튜디오 필터 (성공)
GET {{baseUrl}}/api/partner/enrollments?studioId=550e8400-e29b-41d4-a716-************
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{partnerAuthToken}}

###
# ✅ 파트너 수강신청 단건 조회 (성공)
GET {{baseUrl}}/api/partner/enrollments/550e8400-e29b-41d4-a716-************
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{partnerAuthToken}}

###############################################################################
# 프론트엔드 개발 가이드
###############################################################################
#
# 🎯 수강신청 프로세스 구현 가이드
#
# 1단계: 클래스 선택 페이지
#   - 사용자가 원하는 클래스와 스케줄 그룹을 선택
#   - classId와 scheduleGroupId를 획득
#
# 2단계: 결제 방법 선택 페이지  
#   - 결제 방법(paymentMethod) 선택: credit_card, kakaopay, naverpay 등
#   - 결제 타입(paymentType) 선택: full_payment(전액), deposit(예약금)
#
# 3단계: 수강신청 요청
#   - POST /api/enrollments로 수강신청 요청
#   - 응답에서 enrollmentId와 결제 정보 획득
#
# 4단계: 결제 진행
#   - 선택한 결제 방법에 따라 외부 결제 서비스 연동
#   - 카카오페이: 카카오페이 SDK 사용
#   - 네이버페이: 네이버페이 SDK 사용  
#   - 신용카드: PG사 결제 모듈 사용
#
# 5단계: 결제 성공 처리
#   - 결제 완료 후 POST /api/payments/success로 결제 성공 처리
#   - transactionId는 결제 서비스에서 제공하는 거래 ID
#
# 6단계: 결과 페이지
#   - 결제 성공 시 수강신청 완료 페이지 표시
#   - 다음 단계(Next Steps) 정보 표시
#   - 실패 시 에러 메시지와 재시도 옵션 제공
#
# 📋 잔액 결제 프로세스 (예약금 결제 후)
#
# 1단계: 마이페이지에서 미결제 수강신청 확인
#   - GET /api/payments/history로 결제 내역 조회
#   - paymentType이 'deposit'이고 remainingAmount > 0인 항목 표시
#
# 2단계: 잔액 결제 진행
#   - POST /api/payments/remaining으로 잔액 결제 요청
#   - 새로운 결제 방법 선택 가능
#
# 3단계: 결제 완료 후 성공 처리
#   - POST /api/payments/success로 결제 성공 처리
#
###############################################################################

###############################################################################
# 테스트 데이터 준비 가이드
###############################################################################
#
# 🎯 테스트 실행 전 준비사항:
#
# 1. 데이터베이스에 테스트 클래스 생성:
#    - 활성 상태(active)인 클래스 템플릿
#    - 유효한 스케줄 그룹이 있는 클래스
#    - 비활성 상태(inactive)인 클래스 (에러 케이스용)
#
# 2. 테스트 회원 계정 준비:
#    - 카카오 로그인으로 회원가입된 계정
#    - 온보딩 완료된 계정
#
# 3. 테스트 파트너 계정 준비:
#    - 파트너 로그인된 계정
#    - ACTIVE 상태인 파트너
#
# 4. 변수 값 업데이트:
#    - @validClassId: 실제 존재하는 클래스 템플릿 ID로 변경
#    - @validScheduleGroupId: 해당 클래스의 유효한 스케줄 그룹 ID로 변경
#    - @memberAuthToken: 실제 로그인된 회원의 토큰으로 변경
#    - @partnerAuthToken: 실제 로그인된 파트너의 토큰으로 변경
#
# 5. 테스트 순서:
#    - 수강신청 성공 케이스 먼저 실행
#    - 결제 성공 처리 실행
#    - 결제 내역 조회로 확인
#    - 잔액 결제 테스트 (예약금 결제한 케이스에서)
#    - 각 에러 케이스는 독립적으로 실행 가능
#
###############################################################################
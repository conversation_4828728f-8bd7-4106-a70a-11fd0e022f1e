# .github/workflows/pr-build-check.yml
name: PR Build Check

# PR 생성 및 업데이트 시 실행
on:
  pull_request:
    types: [opened, synchronize]
    branches: [ main, develop ]

jobs:
  build-check:
    runs-on: ubuntu-latest

    steps:
      # 코드 체크아웃
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      # Node.js 설정
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18' # 프로젝트에 맞는 Node.js 버전으로 변경
          cache: 'npm'

      # 의존성 설치
      - name: Install dependencies
        run: npm ci

      # 빌드 실행
      - name: Run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          NEXT_PUBLIC_BASE_URL: ${{ secrets.NEXT_PUBLIC_BASE_URL }}
          NEXT_PUBLIC_KAKAO_REST_API_KEY: ${{ secrets.NEXT_PUBLIC_KAKAO_REST_API_KEY }}
          NEXT_PUBLIC_KAKAO_MAP_API_KEY: ${{ secrets.NEXT_PUBLIC_KAKAO_MAP_API_KEY }}
          KAKAO_CLIENT_SECRET: ${{ secrets.KAKAO_CLIENT_SECRET }}
          NEXT_PUBLIC_TOSS_PAYMENTS_CLIENT_KEY: ${{ secrets.NEXT_PUBLIC_TOSS_PAYMENTS_CLIENT_KEY }}
          TOSS_PAYMENTS_SECRET_KEY: ${{ secrets.TOSS_PAYMENTS_SECRET_KEY }}
          NEXT_PUBLIC_PORTONE_USER_CODE: ${{ secrets.NEXT_PUBLIC_PORTONE_USER_CODE }}
          PORTONE_API_KEY: ${{ secrets.PORTONE_API_KEY }}
          PORTONE_API_SECRET: ${{ secrets.PORTONE_API_SECRET }}
          NEXT_PUBLIC_NAVER_MAP_CLIENT_ID: ${{ secrets.NEXT_PUBLIC_NAVER_MAP_CLIENT_ID }}
          ADMIN_EMAILS: ${{ secrets.ADMIN_EMAILS }}
          NODE_ENV: production
        run: npm run build
